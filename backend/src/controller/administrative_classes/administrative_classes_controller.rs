use std::collections::{HashMap, HashSet};

use axum::{
    extract::{Path, State},
    http::HeaderMap,
    routing::{get, post},
    <PERSON><PERSON>, Router,
};
use uuid::Uuid;

use crate::{
    middleware::auth_middleware::AuthExtractor,
    model::{
        administrative_classes::administrative_classes::{
            AdministrativeClasses, AdministrativeClassesDetail, AdministrativeClassesStatistics, CreateAdministrativeClassesParams, DeleteAdministrativeClassesParams, FindAllStudentInClassParams, MoveStudentToAdministrativeClassesParams, PageStudentInClassParams, PageUserClassListParams, RemoveStudentFromAdministrativeClassesParams, UpdateAdministrativeClassesParams
        },
        Student,
    },
    utils::api_response::{responses, ApiResponse, PaginatedApiResponse},
    web_server::AppState,
};

pub fn create_router() -> Router<AppState> {
    Router::new()
        .route("/getStatistics", get(get_statistics))
        .route("/getUserClassList", get(get_user_class_list))
        .route("/pageUserClassList",post(page_user_class_list))
        .route("/createClasses", post(create_classes))
        .route("/findAllStudentInClass", post(find_all_student_in_class))
        .route("/updateClasses", post(update_classes))
        .route(
            "/moveStudentToAdministrativeClasses",
            post(move_student_to_administrative_classes),
        )
        .route(
            "/removeStudentFromAdministrativeClasses",
            post(remove_student_from_administrative_classes),
        )
        .route("/pageStudentInClass",post(page_student_in_class))
        .route("/deleteClass", post(delete_class))
}

pub async fn get_statistics(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    header_map: HeaderMap,
    AuthExtractor(context): AuthExtractor,
) -> Result<ApiResponse<AdministrativeClassesStatistics>, ApiResponse<()>> {
    let tenant_id = context.get_tenant_id_from_headers(header_map);
    match state
        .administrative_classes_service
        .get_statistics(&context, &tenant_id, &tenant_name, &context.user_id)
        .await
    {
        Ok(data) => Ok(responses::success(data, None)),
        Err(msg) => Err(responses::error(&msg, None)),
    }
}
/**
 * 该路由未使用，待删除
 */
pub async fn get_user_class_list(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    header_map: HeaderMap,
    AuthExtractor(context): AuthExtractor,
) -> Result<ApiResponse<Vec<AdministrativeClassesDetail>>, ApiResponse<()>> {
    //查询班级列表
    let mut class_list: Vec<AdministrativeClasses> = vec![];
    let tenant_id = context.get_tenant_id_from_headers(header_map);
    if context.is_admin_in_tenant(tenant_id) {
        //管理员
        class_list.append(
            &mut state
                .administrative_classes_service
                .get_all_class_list(&tenant_name)
                .await
                .map_err(|e| responses::error(&e, None))?,
        );
    } else {
        class_list.append(
            &mut state
                .administrative_classes_service
                .get_user_class_list(&tenant_name, &context.user_id)
                .await
                .map_err(|e| responses::error(&e, None))?,
        );
    }
    //联查额外信息
    let mut teacher_id_set = HashSet::<Uuid>::new();
    let mut grade_level_code_set = HashSet::<String>::new();
    let mut class_id_set = HashSet::<Uuid>::new();
    class_list.iter().for_each(|c: &AdministrativeClasses| {
        if c.teacher_id.is_some() {
            teacher_id_set.insert(c.teacher_id.clone().unwrap());
        }
        if c.grade_level_code.is_some() {
            grade_level_code_set.insert(c.grade_level_code.clone().unwrap());
        }
        class_id_set.insert(c.id.clone());
    });
    //联查教师信息
    let teacher_list = state
        .teacher_service
        .find_all_by_id_in(&tenant_name, teacher_id_set.into_iter().collect())
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))?;
    let mut teacher_id_to_name_map = HashMap::<Uuid, String>::new();
    teacher_list.iter().for_each(|teacher| {
        teacher_id_to_name_map.insert(teacher.id, teacher.teacher_name.clone());
    });
    //联查年级信息
    let grade_level_all_list = state
        .grade_service
        .get_all_grades()
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))?;
    let mut grade_level_code_to_name_map = HashMap::<String, String>::new();
    grade_level_all_list.iter().for_each(|item| {
        grade_level_code_to_name_map.insert(item.code.clone(), item.name.clone());
    });
    //联查班级人数统计信息
    let class_id_to_student_count_map = state
        .student_service
        .batch_count_by_class(&tenant_name, &class_id_set.into_iter().collect())
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))?;
    Ok(responses::success(
        class_list
            .into_iter()
            .map(|classes| {
                let AdministrativeClasses {
                    id,
                    class_name,
                    code,
                    academic_year,
                    grade_level_code,
                    teacher_id,
                    created_at,
                    updated_at,
                    is_active,
                } = classes;
                AdministrativeClassesDetail {
                    id,
                    class_name,
                    code,
                    academic_year,
                    grade_level_code: grade_level_code.clone(),
                    teacher_id,
                    created_at,
                    updated_at,
                    is_active,
                    teacher_name: teacher_id.map_or(None, |teacher_id| {
                        teacher_id_to_name_map
                            .get(&teacher_id)
                            .map_or(None, |v| Some(v.clone()))
                    }),
                    grade_level_name: grade_level_code.map_or(None, |item| {
                        grade_level_code_to_name_map
                            .get(&item)
                            .map_or(None, |v| Some(v.clone()))
                    }),
                    total_student: class_id_to_student_count_map.get(&id).unwrap_or(&0).clone(),
                }
            })
            .collect(),
        None,
    ))
}

pub async fn create_classes(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    AuthExtractor(admin_context): AuthExtractor,
    Json(params): Json<CreateAdministrativeClassesParams>,
) -> Result<ApiResponse<AdministrativeClasses>, ApiResponse<()>> {
    match state
        .administrative_classes_service
        .create_classes(&tenant_name, &admin_context.user_id, &params)
        .await
    {
        Ok(data) => Ok(responses::success(data, None)),
        Err(msg) => Err(responses::error(msg.to_string().as_str(), None)),
    }
}

pub async fn find_all_student_in_class(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    AuthExtractor(_context): AuthExtractor,
    Json(params): Json<FindAllStudentInClassParams>,
) -> Result<ApiResponse<Vec<Student>>, ApiResponse<()>> {
    state
        .administrative_classes_service
        .find_all_student_in_class(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))
        .map(|data| responses::success(data, None))
}

pub async fn update_classes(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<UpdateAdministrativeClassesParams>,
) -> Result<ApiResponse<()>, ApiResponse<()>> {
    state
        .administrative_classes_service
        .update_classes(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))
        .map(|_| responses::success_no_data(None))
}

pub async fn move_student_to_administrative_classes(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<MoveStudentToAdministrativeClassesParams>,
) -> Result<ApiResponse<()>, ApiResponse<()>> {
    state
        .administrative_classes_service
        .move_student_to_administrative_classes(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))
        .map(|_| responses::success_no_data(None))
}

pub async fn remove_student_from_administrative_classes(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<RemoveStudentFromAdministrativeClassesParams>,
) -> Result<ApiResponse<()>, ApiResponse<()>> {
    state
        .administrative_classes_service
        .remove_student_from_administrative_classes(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))
        .map(|_| responses::success_no_data(None))
}

pub async fn delete_class(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<DeleteAdministrativeClassesParams>,
) -> Result<ApiResponse<()>, ApiResponse<()>> {
    state
        .administrative_classes_service
        .delete_class(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|_| responses::success_no_data(None))
}

/**
 * 作者：朱若表
 * 说明：分页查询班级内的学生
 */
pub async fn page_student_in_class(
    State(state): State<AppState>,
    AuthExtractor(context): AuthExtractor,
    _header_map: HeaderMap,
    Path(tenant_name): Path<String>,
    Json(params): Json<PageStudentInClassParams>,
) -> Result<PaginatedApiResponse<Student>, PaginatedApiResponse<()>> {
    state
        .administrative_classes_service
        .page_student_in_class(&context,&tenant_name, &params)
        .await
        .map_err(|e| responses::paginated_error(e.to_string().as_str(), None))
        .map(|(list, count)| {
            responses::paginated_success(
                list,
                params.page_params.get_page(),
                params.page_params.get_page_size(),
                count,
                None,
            )
        })
}
/**
 * 作者：朱若彪
 * 说明：分页查询班级列表
 */
pub async fn page_user_class_list(
    State(state): State<AppState>,
    AuthExtractor(context): AuthExtractor,
    Path(tenant_name): Path<String>,
    header_map: HeaderMap,
    Json(params): Json<PageUserClassListParams>,
) -> Result<PaginatedApiResponse<AdministrativeClassesDetail>, PaginatedApiResponse<()>> {
    //查询班级列表
    let mut class_list: Vec<AdministrativeClasses> = vec![];
    let tenant_id = context.get_tenant_id_from_headers(header_map);
    let mut total_count=0;
    if context.is_admin_in_tenant(tenant_id) {
        let (mut list,count)= state
            .administrative_classes_service
            .page_all_class_list(&tenant_name, &params)
            .await
            .map_err(|e| responses::paginated_error(e.to_string().as_str(), None))?;
        class_list.append(&mut list);
        total_count=count;
    } else {
        class_list.append(
            &mut state
                .administrative_classes_service
                .get_user_class_list(&tenant_name, &context.user_id)
                .await
                .map_err(|e| responses::paginated_error(e.to_string().as_str(), None))?,
        );
    }
    //联查额外信息
    let mut teacher_id_set = HashSet::<Uuid>::new();
    let mut grade_level_code_set = HashSet::<String>::new();
    let mut class_id_set = HashSet::<Uuid>::new();
    class_list.iter().for_each(|c: &AdministrativeClasses| {
        if c.teacher_id.is_some() {
            teacher_id_set.insert(c.teacher_id.clone().unwrap());
        }
        if c.grade_level_code.is_some() {
            grade_level_code_set.insert(c.grade_level_code.clone().unwrap());
        }
        class_id_set.insert(c.id.clone());
    });
    //联查教师信息
    let teacher_list = state
        .teacher_service
        .find_all_by_id_in(&tenant_name, teacher_id_set.into_iter().collect())
        .await
        .map_err(|e| responses::paginated_error(e.to_string().as_str(), None))?;
    let mut teacher_id_to_name_map = HashMap::<Uuid, String>::new();
    teacher_list.iter().for_each(|teacher| {
        teacher_id_to_name_map.insert(teacher.id, teacher.teacher_name.clone());
    });
    //联查年级信息
    let grade_level_all_list = state
        .grade_service
        .get_all_grades()
        .await
        .map_err(|e| responses::paginated_error(e.to_string().as_str(), None))?;
    let mut grade_level_code_to_name_map = HashMap::<String, String>::new();
    grade_level_all_list.iter().for_each(|item| {
        grade_level_code_to_name_map.insert(item.code.clone(), item.name.clone());
    });
    //联查班级人数统计信息
    let class_id_to_student_count_map = state
        .student_service
        .batch_count_by_class(&tenant_name, &class_id_set.into_iter().collect())
        .await
        .map_err(|e| responses::paginated_error(e.to_string().as_str(), None))?;
    Ok(responses::paginated_success(
        class_list.clone()
            .into_iter()
            .map(|classes| {
                let AdministrativeClasses {
                    id,
                    class_name,
                    code,
                    academic_year,
                    grade_level_code,
                    teacher_id,
                    created_at,
                    updated_at,
                    is_active,
                } = classes;
                AdministrativeClassesDetail {
                    id,
                    class_name,
                    code,
                    academic_year,
                    grade_level_code: grade_level_code.clone(),
                    teacher_id,
                    created_at,
                    updated_at,
                    is_active,
                    teacher_name: teacher_id.map_or(None, |teacher_id| {
                        teacher_id_to_name_map
                            .get(&teacher_id)
                            .map_or(None, |v| Some(v.clone()))
                    }),
                    grade_level_name: grade_level_code.map_or(None, |item| {
                        grade_level_code_to_name_map
                            .get(&item)
                            .map_or(None, |v| Some(v.clone()))
                    }),
                    total_student: class_id_to_student_count_map.get(&id).unwrap_or(&0).clone(),
                }
            })
            .collect(),
            params.page_params.get_page(),
            params.page_params.get_page_size(),
            total_count,
        None,
    ))
}