use crate::middleware::auth_middleware::AuthExtractor;
use crate::model::{CreateStudentParams, FindAllStudentParams, Student, StudentVo, UpdateStudentParams};
use crate::utils::api_response::{responses, ApiResponse, PaginatedApiResponse};
use crate::web_server::AppState;
use axum::http::HeaderMap;
use axum::{
    extract::{Path, State},
    routing::post,
    Json, Router,
};
use tracing::{info};

pub fn create_router() -> Router<AppState> {
    Router::new()
        .route("/pageAllStudent", post(page_all_student))
        .route("/createStudent", post(create_student))
        .route("/updateStudent", post(update_student))
}

pub async fn page_all_student(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    header_map: HeaderMap,
    AuthExtractor(context): Auth<PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>(params): <PERSON><PERSON><FindAllStudentParams>,
) -> Result<PaginatedApiResponse<StudentVo>, PaginatedApiResponse<()>> {
    info!("User roles: {:?}", context.roles);
    info!("User ID: {}, Tenant: {}", context.user_id, tenant_name);

    // Casbin permission check
    // let mut has_permission = false;
    //
    // if context.roles.is_empty() {
    //     info!("No roles found for user: {}", context.user_id);
    //     return Err(responses::paginated_error("用户无角色信息！", None));
    // }
    //
    // for role in &context.roles {
    //     let req = PermissionRequest {
    //         subject: role.identity_type.clone(),
    //         domain: tenant_name.clone(),
    //         object: "student".to_string(),
    //         action: "read".to_string(),
    //     };
    //
    //     info!("Checking permission for role: {} with request: {:?}", role.identity_type, req);
    //
    //     match state.casbin_service.enforce(&req).await {
    //         Ok(result) => {
    //             info!("Permission check result for role {}: {}", role.identity_type, result);
    //             if result {
    //                 has_permission = true;
    //                 info!("Permission granted for role: {}", role.identity_type);
    //                 break;
    //             }
    //         },
    //         Err(e) => {
    //             info!("Permission check error for role {}: {}", role.identity_type, e);
    //         }
    //     }
    // }
    //
    // if !has_permission {
    //     info!("No permission found for user {} in tenant {}", context.user_id, tenant_name);
    //     return Err(responses::paginated_error("权限不足！", None));
    // }

    info!("Permission granted, proceeding with student query");
    let tenant_id = context.get_tenant_id_from_headers(header_map);

    // 检查用户权限：管理员可以查看所有学生，班主任只能查看自己负责班级的学生
    let user_id_for_filter = if context.is_admin_in_tenant(tenant_id) {
        // 管理员不需要过滤，传递None表示查看所有学生
        None
    } else {
        // 其他有权限的角色，需要根据其ID进行权限过滤
        Some(context.user_id)
    };

    state
        .student_service
        .page_all_student(&tenant_name, &params, user_id_for_filter, Some(tenant_name.clone()), Some(state.casbin_service.as_ref()))
        .await
        .map_err(|e| responses::paginated_error(&e, None))
        .map(|(list, total)| {
            responses::paginated_success(
                list,
                params.page_params.get_page(),
                params.page_params.get_page_size(),
                total,
                None,
            )
        })
}

pub async fn create_student(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    header_map: HeaderMap,
    AuthExtractor(context): AuthExtractor,
    Json(params): Json<CreateStudentParams>,
) -> Result<ApiResponse<Student>, ApiResponse<()>> {
    let tenant_id = context.get_tenant_id_from_headers(header_map);
    if !context.is_admin_in_tenant(tenant_id) {
        return Err(responses::error("权限不足！", None));
    }
    state
        .student_service
        .create_student(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|data| responses::success(data, None))
}

pub async fn update_student(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    header_map: HeaderMap,
    AuthExtractor(context): AuthExtractor,
    Json(params): Json<UpdateStudentParams>,
) -> Result<ApiResponse<Student>, ApiResponse<()>> {
    let tenant_id = context.get_tenant_id_from_headers(header_map);
    if !context.is_admin_in_tenant(tenant_id) {
        return Err(responses::error("权限不足！", None));
    }
    state
        .student_service
        .update_student(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|data| responses::success(data, None))
}
