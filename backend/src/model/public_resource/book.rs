use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

// Book: "Learning Rust"
// ├── Part 1: Basics
// │   ├── Chapter 1: Getting Started
// │   │   ├── Section 1.1: Installation
// │   │   └── Section 1.2: Hello World
// │   │       ├── Subsection 1.2.1: Code Structure
// │   │       └── Paragraph: "Rust is a systems programming language..."
// │   └── Chapter 2: Variables
// └── Part 2: Advanced
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
#[serde(rename_all = "camelCase")]
pub struct Book {
    pub id: Uuid,
    pub title: String,
    pub subject_code: Option<String>, // 学科
    pub grade_level_code: Option<String>, // 年级
    pub publisher: Option<String>, // 出版社
    pub distributor: Option<String>, // 发行机构
    pub year: Option<i32>, // 出版年份
    pub cover_path: Option<String>, // 封面
    pub isbn: Option<String>,
    pub edition: Option<String>, // 版本
    pub printing_version: Option<String>, // 印次
    pub authors: Option<Vec<String>>, // 作者
    pub summary: Option<String>, // 简介或摘要
    // pub language: Option<String>, // 语言
    pub updated_at: DateTime<Utc>,
}

impl Book {
    pub fn default() -> Self {
        Self {
            id: Uuid::new_v4(),
            title: "测试书本".to_string(),
            subject_code: Some("YW".to_string()),
            grade_level_code: Some("GZ2".to_string()),
            publisher: None,
            distributor: None,
            year: Some(2019),
            cover_path: None,
            isbn: Some("iSBN".to_string()),
            edition: Some("ED1".to_string()),
            printing_version: None,
            authors: None,
            summary: None,
            updated_at: Default::default(),
        }
    }
}