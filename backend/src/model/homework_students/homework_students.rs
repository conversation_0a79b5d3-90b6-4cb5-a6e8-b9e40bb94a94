use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;
use qc_sqlx_derive::QcSqlxEnum;
use crate::model::PageParams;

#[derive(Debug, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct HomeworkStudents {
    pub id: Uuid,
    pub homework_id: Uuid,
    pub student_id: Uuid,
    pub class_id: Uuid,
    pub status: HomeworkStudentStatus,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}
#[derive(Clone, Debug, PartialEq, Eq, QcSqlxEnum, Serialize, Deserialize)]
#[serde(rename = "<PERSON>C<PERSON>")]
pub enum HomeworkStudentStatus {
    Unsubmitted,
    Error,
    Done,
}

#[derive(Debug, <PERSON>lone, FromRow, Serialize, Deserialize)]
pub struct PageStudentsByHomeworkIdParams {
    pub homework_id: Uuid,
    pub page_params: <PERSON><PERSON>ara<PERSON>,
    pub name_like: Option<String>,
    pub student_number: Option<String>,
    pub administrative_class_id: Option<Uuid>,
}