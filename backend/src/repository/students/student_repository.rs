use serde::Serialize;
use sqlx::{pool::PoolConnection, PgPool, Postgres};
use uuid::Uuid;
use anyhow::Result;

use crate::model::{PageParams, Student, StudentBaseInfo};


/**
 * 学生表数据库方法
 */

pub struct StudentsRepository {}
impl StudentsRepository {
    /**
     * 作者：朱若彪
     * 说明：分页查询班级内学生
     */
    pub async fn page_student_in_class(
        conn: &mut PoolConnection<Postgres>,
        class_id: &Uuid,
        page_params: &PageParams,
        name_like: &Option<String>,
        status: &Option<String>,
        student_number: &Option<String>,
    )->Result<(Vec<Student>,i64),String>{
        //select * from students s where s.class_id = '' and s.name like '' and s.status='' and s.student_number='' order by s.created_at desc
        let mut query_builder=sqlx::QueryBuilder::new("select s.* from students s where s.administrative_class_id = ");
        let mut count_builder=sqlx::QueryBuilder::new("select count(*) from students s where s.administrative_class_id = ");
        query_builder.push_bind(class_id);
        count_builder.push_bind(class_id);
        //添加查询条件
        if let Some(name_like_val)=name_like{
            if !name_like_val.trim().is_empty(){
                query_builder.push(" and s.student_name like ").push_bind(format!("%{}%",name_like_val));
                count_builder.push(" and s.student_name like ").push_bind(format!("%{}%",name_like_val));
            }
        }
        if let Some(status_val)=status{
            if !status_val.trim().is_empty(){
                query_builder.push(" and s.status = ").push_bind(status_val);
                count_builder.push(" and s.status = ").push_bind(status_val);
            }
        }
        if let Some(student_number_val)=student_number{
            if !student_number_val.trim().is_empty(){
                query_builder.push(" and s.student_number = ").push_bind(student_number_val);
                count_builder.push(" and s.student_number = ").push_bind(student_number_val);
            }
        }
        //添加分页和排序
        query_builder.push(" order by s.created_at desc limit ").push_bind(page_params.get_limit());
        query_builder.push(" offset ").push_bind(page_params.get_offset());
        //执行sql
        let list = query_builder
            .build_query_as::<Student>()
            .fetch_all(conn.as_mut())
            .await
            .map_err(|e| e.to_string())?;
        let count = count_builder
            .build_query_scalar()
            .fetch_one(conn.as_mut())
            .await
            .map_err(|e| e.to_string())?;
        Ok((list, count))
    }
    /**
     * 作者：朱若彪
     * 说明：分页查询作业内学生
     */
    pub async fn page_students_by_homework_id(
        conn: &mut PoolConnection<Postgres>,
        id_list: Vec<Uuid>,
        page_params: &PageParams,
        name_like: &Option<String>,
        student_number: &Option<String>,
    )->Result<(Vec<StudentBaseInfo>,i64),String>{
        //select * from students s where s.id in () and s.name like '' and s.student_number='' and s.administrative_class_id='' order by s.created_at desc limit 10 offset 0
        let mut query_builder=sqlx::QueryBuilder::new("select s.* from students s where s.id in (");
        let mut count_builder=sqlx::QueryBuilder::new("select count(*) from students s where s.id in (");
        if id_list.len() == 0 {
            return Ok((vec![],0));
        }
        for (i, id) in id_list.iter().enumerate() {
            query_builder.push_bind(id);
            count_builder.push_bind(id);
            if i < id_list.len() - 1 {
                query_builder.push(" , ");
                count_builder.push(" , ");
            }
        }
        query_builder.push(" )");
        count_builder.push(" )");
        //添加查询条件
        if let Some(name_like_val) = name_like {
            if !name_like_val.trim().is_empty() {
                query_builder.push(" and s.student_name like ").push_bind(format!("%{}%", name_like_val));
                count_builder.push(" and s.student_name like ").push_bind(format!("%{}%", name_like_val));
            }
        }
        if let Some(student_number_val) = student_number {
            if !student_number_val.trim().is_empty() {
                query_builder.push(" and s.student_number = ").push_bind(student_number_val);
                count_builder.push(" and s.student_number = ").push_bind(student_number_val);
            }
        }
        //添加分页和排序
        query_builder.push(" order by s.created_at desc limit ").push_bind(page_params.get_limit());
        query_builder.push(" offset ").push_bind(page_params.get_offset());
        //执行sql
        let list = query_builder
            .build_query_as::<StudentBaseInfo>()
            .fetch_all(conn.as_mut())
            .await
            .map_err(|e| e.to_string())?;
        let count = count_builder
            .build_query_scalar()
            .fetch_one(conn.as_mut())
            .await
            .map_err(|e| e.to_string())?;
        Ok((list, count))
    }
    pub async fn page_student_by_teaching_class_id(
        conn: &mut PoolConnection<Postgres>,
        teaching_class_id: &Uuid,
        page_params: &PageParams,
        name_like: &Option<String>,
        status: &Option<String>,
        student_number: &Option<String>,
    )->Result<(Vec<Student>,i64),String>{
        //select s.* from student s,student_teaching_classes stc where stc.class_id = '' and s.id = stc.student_id and s.name like '' and s.status='' and s.student_number='' order by s.created_at desc limit 10 offset 0
        let mut query_builder=sqlx::QueryBuilder::new("select s.* from students s,student_teaching_classes stc where stc.class_id = ");
        let mut count_builder=sqlx::QueryBuilder::new("select count(*) from students s,student_teaching_classes stc where stc.class_id = ");
        query_builder.push_bind(teaching_class_id);
        count_builder.push_bind(teaching_class_id);
        
        // 添加JOIN条件
        query_builder.push(" and s.id = stc.student_id");
        count_builder.push(" and s.id = stc.student_id");
        
        //添加查询条件
        if let Some(name_like_val)=name_like{
            if !name_like_val.trim().is_empty(){
                query_builder.push(" and s.student_name like ").push_bind(format!("%{}%",name_like_val));
                count_builder.push(" and s.student_name like ").push_bind(format!("%{}%",name_like_val));
            }
        }
        if let Some(status_val)=status{
            if !status_val.trim().is_empty(){
                query_builder.push(" and s.status = ").push_bind(status_val);
                count_builder.push(" and s.status = ").push_bind(status_val);
            }
        }
        if let Some(student_number_val)=student_number{
            if !student_number_val.trim().is_empty(){
                query_builder.push(" and s.student_number = ").push_bind(student_number_val);
                count_builder.push(" and s.student_number = ").push_bind(student_number_val);
            }
        }
        //添加分页和排序
        query_builder.push(" order by s.created_at desc limit ").push_bind(page_params.get_limit());
        query_builder.push(" offset ").push_bind(page_params.get_offset());
        //执行sql
        let list = query_builder
            .build_query_as::<Student>()
            .fetch_all(conn.as_mut())
            .await
            .map_err(|e| e.to_string())?;
        let count = count_builder
            .build_query_scalar()
            .fetch_one(conn.as_mut())
            .await
            .map_err(|e| e.to_string())?;
        Ok((list, count))
    }
    pub async fn fetch_students_by_ids(db: &PgPool, tenant_name: &str, student_ids: Vec<Uuid>) -> anyhow::Result<Vec<StudentBase>> {
        let mut builder=sqlx::QueryBuilder::new(format!("select id as student_id, student_number, student_name from {}.students where id = ANY(", tenant_name));
        builder.push_bind(student_ids).push(")");
        let ret = builder.build_query_as().fetch_all(db).await?;
        Ok(ret)
    }

    /// 获取用户的学生ID（如果用户是学生）
    pub async fn get_user_student_id(
        pool: &PgPool,
        user_id: &Uuid,
        schema_name: &str,
    ) -> Result<Option<Uuid>> {
        let query = format!(
            r#"
            SELECT s.id FROM {}.students s
            JOIN {}.user_identities ui ON s.user_id = ui.user_id
            WHERE ui.user_id = $1 AND ui.role_id IN (
                SELECT id FROM public.roles WHERE code = 'student'
            )
            LIMIT 1
            "#,
            schema_name, schema_name
        );

        let student_id: Option<Uuid> = sqlx::query_scalar(&query)
            .bind(user_id)
            .fetch_optional(pool)
            .await?;

        Ok(student_id)
    }

    /// 获取用户的子女ID列表（如果用户是家长）
    pub async fn get_user_children_ids(
        pool: &PgPool,
        user_id: &Uuid,
        schema_name: &str,
    ) -> Result<Option<Vec<Uuid>>> {
        let query = format!(
            r#"
            SELECT s.id FROM {}.students s
            JOIN {}.student_parents sp ON s.id = sp.student_id
            WHERE sp.parent_user_id = $1
            "#,
            schema_name, schema_name
        );

        let children_ids: Vec<Uuid> = sqlx::query_scalar(&query)
            .bind(user_id)
            .fetch_all(pool)
            .await?;

        if children_ids.is_empty() {
            Ok(None)
        } else {
            Ok(Some(children_ids))
        }
    }
}
#[derive(Debug, Clone, Serialize, sqlx::FromRow)]
pub struct StudentBase {
    pub student_id: Uuid,
    pub student_number: String,
    pub student_name: String,
}