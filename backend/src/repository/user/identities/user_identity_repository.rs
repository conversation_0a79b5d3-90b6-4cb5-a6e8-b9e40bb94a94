use crate::controller::user::identities::user_identity_controller::{
    BatchOperationResponse, BatchUserIdentityRequest, CreateUserIdentityRequest,
    QueryUserIdentitiesParams, RoleInfo, UpdateUserIdentityRequest, UserIdentitiesListResponse,
    UserIdentityResponse, BatchFailedItem,
};
use crate::model::user::auth::UserIdentity;
use anyhow::{anyhow, Result};
use chrono::Utc;
use sqlx::{PgPool, Row};
use std::collections::HashMap;
use uuid::Uuid;

pub struct UserIdentityRepository {
    pool: PgPool,
}

impl UserIdentityRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 创建用户身份（支持多角色）
    pub async fn create_user_identity(
        &self,
        request: CreateUserIdentityRequest,
        schema_name: &str,
    ) -> Result<UserIdentityResponse> {
        let mut tx = self.pool.begin().await?;
        let now = Utc::now();
        let mut created_identities = Vec::new();

        // 为每个角色创建一个身份记录
        for role_id in &request.role_ids {
            let identity_id = Uuid::new_v4();
            
            let query = format!(
                r#"
                INSERT INTO "{schema_name}".user_identities 
                (id, user_id, role_id, target_type, target_id, subject, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                RETURNING id, user_id, role_id, target_type, target_id, subject, created_at, updated_at
                "#
            );

            let identity = sqlx::query_as::<_, UserIdentity>(&query)
                .bind(identity_id)
                .bind(request.user_id)
                .bind(role_id)
                .bind(&request.target_type)
                .bind(request.target_id)
                .bind(&request.subject)
                .bind(now)
                .bind(now)
                .fetch_one(&mut *tx)
                .await?;

            created_identities.push(identity);
        }

        tx.commit().await?;

        // 获取角色信息
        let roles = self.get_roles_by_ids(&request.role_ids).await?;

        // 返回第一个创建的身份作为代表（实际上应该返回组合的身份信息）
        if let Some(first_identity) = created_identities.first() {
            Ok(UserIdentityResponse {
                id: first_identity.id,
                user_id: first_identity.user_id,
                roles,
                target_type: first_identity.target_type.clone(),
                target_id: first_identity.target_id,
                subject: first_identity.subject.clone(),
                created_at: first_identity.created_at,
                updated_at: first_identity.updated_at,
            })
        } else {
            Err(anyhow!("未能创建任何用户身份"))
        }
    }

    /// 获取用户身份列表
    pub async fn get_user_identities(
        &self,
        params: QueryUserIdentitiesParams,
        schema_name: &str,
    ) -> Result<UserIdentitiesListResponse> {
        let page = params.page.unwrap_or(1);
        let page_size = params.page_size.unwrap_or(20);
        let offset = (page - 1) * page_size;

        let mut where_conditions = Vec::new();
        let mut param_index = 1;

        // 构建查询条件
        if params.user_id.is_some() {
            where_conditions.push(format!("ui.user_id = ${}", param_index));
            param_index += 1;
        }

        if params.role_id.is_some() {
            where_conditions.push(format!("ui.role_id = ${}", param_index));
            param_index += 1;
        }

        if params.target_type.is_some() {
            where_conditions.push(format!("ui.target_type = ${}", param_index));
            param_index += 1;
        }

        if params.target_id.is_some() {
            where_conditions.push(format!("ui.target_id = ${}", param_index));
            param_index += 1;
        }

        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };

        // 查询总数
        let count_query = format!(
            r#"
            SELECT COUNT(*) as total
            FROM (
                SELECT DISTINCT ui.user_id, ui.target_type, ui.target_id, ui.subject
                FROM "{schema_name}".user_identities ui
                JOIN public.roles r ON ui.role_id = r.id
                {where_clause}
            ) as distinct_identities
            "#
        );

        let mut count_query_builder = sqlx::query(&count_query);

        // 绑定参数
        if let Some(user_id) = params.user_id {
            count_query_builder = count_query_builder.bind(user_id);
        }
        if let Some(role_id) = params.role_id {
            count_query_builder = count_query_builder.bind(role_id);
        }
        if let Some(target_type) = &params.target_type {
            count_query_builder = count_query_builder.bind(target_type);
        }
        if let Some(target_id) = params.target_id {
            count_query_builder = count_query_builder.bind(target_id);
        }

        let total: i64 = count_query_builder
            .fetch_one(&self.pool)
            .await?
            .try_get("total")?;

        // 查询数据
        let data_query = format!(
            r#"
            SELECT
                ui.id,
                ui.user_id,
                ui.role_id,
                ui.target_type,
                ui.target_id,
                ui.subject,
                ui.created_at,
                ui.updated_at,
                r.name as role_name,
                r.code as role_code,
                r.description as role_description
            FROM "{schema_name}".user_identities ui
            JOIN public.roles r ON ui.role_id = r.id
            {where_clause}
            ORDER BY ui.created_at DESC
            LIMIT ${} OFFSET ${}
            "#,
            param_index, param_index + 1
        );

        let mut data_query_builder = sqlx::query(&data_query);

        // 绑定参数
        if let Some(user_id) = params.user_id {
            data_query_builder = data_query_builder.bind(user_id);
        }
        if let Some(role_id) = params.role_id {
            data_query_builder = data_query_builder.bind(role_id);
        }
        if let Some(target_type) = &params.target_type {
            data_query_builder = data_query_builder.bind(target_type);
        }
        if let Some(target_id) = params.target_id {
            data_query_builder = data_query_builder.bind(target_id);
        }

        // 绑定分页参数
        data_query_builder = data_query_builder
            .bind(page_size as i64)
            .bind(offset as i64);

        let rows = data_query_builder
            .fetch_all(&self.pool)
            .await?;

        // 按用户和目标分组，合并角色
        let mut identity_map: HashMap<(Uuid, String, Option<Uuid>, Option<String>), UserIdentityResponse> = HashMap::new();

        for row in rows {
            let user_id: Uuid = row.try_get("user_id")?;
            let target_type: String = row.try_get("target_type")?;
            let target_id: Option<Uuid> = row.try_get("target_id")?;
            let subject: Option<String> = row.try_get("subject")?;
            
            let key = (user_id, target_type.clone(), target_id, subject.clone());
            
            let role_info = RoleInfo {
                id: row.try_get("role_id")?,
                name: row.try_get("role_name")?,
                code: row.try_get("role_code")?,
                description: row.try_get("role_description")?,
            };

            if let Some(identity) = identity_map.get_mut(&key) {
                identity.roles.push(role_info);
            } else {
                let identity = UserIdentityResponse {
                    id: row.try_get("id")?,
                    user_id,
                    roles: vec![role_info],
                    target_type,
                    target_id,
                    subject,
                    created_at: row.try_get("created_at")?,
                    updated_at: row.try_get("updated_at")?,
                };
                identity_map.insert(key, identity);
            }
        }

        let identities: Vec<UserIdentityResponse> = identity_map.into_values().collect();

        Ok(UserIdentitiesListResponse {
            identities,
            total: total as u64,
            page,
            page_size,
        })
    }

    /// 根据ID获取用户身份
    pub async fn get_user_identity_by_id(
        &self,
        identity_id: Uuid,
        schema_name: &str,
    ) -> Result<Option<UserIdentityResponse>> {
        let query = format!(
            r#"
            SELECT 
                ui.id,
                ui.user_id,
                ui.role_id,
                ui.target_type,
                ui.target_id,
                ui.subject,
                ui.created_at,
                ui.updated_at,
                r.name as role_name,
                r.code as role_code,
                r.description as role_description
            FROM "{schema_name}".user_identities ui
            JOIN public.roles r ON ui.role_id = r.id
            WHERE ui.id = $1
            "#
        );

        let row = sqlx::query(&query)
            .bind(identity_id)
            .fetch_optional(&self.pool)
            .await?;

        if let Some(row) = row {
            let role_info = RoleInfo {
                id: row.try_get("role_id")?,
                name: row.try_get("role_name")?,
                code: row.try_get("role_code")?,
                description: row.try_get("role_description")?,
            };

            Ok(Some(UserIdentityResponse {
                id: row.try_get("id")?,
                user_id: row.try_get("user_id")?,
                roles: vec![role_info],
                target_type: row.try_get("target_type")?,
                target_id: row.try_get("target_id")?,
                subject: row.try_get("subject")?,
                created_at: row.try_get("created_at")?,
                updated_at: row.try_get("updated_at")?,
            }))
        } else {
            Ok(None)
        }
    }

    /// 获取角色信息
    async fn get_roles_by_ids(&self, role_ids: &[Uuid]) -> Result<Vec<RoleInfo>> {
        if role_ids.is_empty() {
            return Ok(Vec::new());
        }

        let placeholders: Vec<String> = (1..=role_ids.len()).map(|i| format!("${}", i)).collect();
        let query = format!(
            "SELECT id, name, code, description FROM public.roles WHERE id IN ({})",
            placeholders.join(", ")
        );

        let mut query_builder = sqlx::query(&query);
        for role_id in role_ids {
            query_builder = query_builder.bind(role_id);
        }

        let rows = query_builder.fetch_all(&self.pool).await?;
        
        let mut roles = Vec::new();
        for row in rows {
            roles.push(RoleInfo {
                id: row.try_get("id")?,
                name: row.try_get("name")?,
                code: row.try_get("code")?,
                description: row.try_get("description")?,
            });
        }

        Ok(roles)
    }

    /// 更新用户身份
    pub async fn update_user_identity(
        &self,
        identity_id: Uuid,
        request: UpdateUserIdentityRequest,
        schema_name: &str,
    ) -> Result<UserIdentityResponse> {
        let mut tx = self.pool.begin().await?;
        let now = Utc::now();

        // 首先获取现有的身份信息
        let existing_query = format!(
            r#"
            SELECT user_id, target_type, target_id, subject
            FROM "{schema_name}".user_identities
            WHERE id = $1
            "#
        );

        let existing_row = sqlx::query(&existing_query)
            .bind(identity_id)
            .fetch_optional(&mut *tx)
            .await?
            .ok_or_else(|| anyhow!("用户身份不存在"))?;

        let user_id: Uuid = existing_row.try_get("user_id")?;
        let current_target_type: String = existing_row.try_get("target_type")?;
        let current_target_id: Option<Uuid> = existing_row.try_get("target_id")?;
        let current_subject: Option<String> = existing_row.try_get("subject")?;

        // 如果提供了新的角色列表，需要删除旧的身份记录并创建新的
        if !request.role_ids.is_empty() {
            // 删除相同目标的所有身份记录
            let delete_query = format!(
                r#"
                DELETE FROM "{schema_name}".user_identities
                WHERE user_id = $1 AND target_type = $2
                AND ($3::uuid IS NULL AND target_id IS NULL OR target_id = $3)
                AND ($4::text IS NULL AND subject IS NULL OR subject = $4)
                "#
            );

            sqlx::query(&delete_query)
                .bind(user_id)
                .bind(&current_target_type)
                .bind(current_target_id)
                .bind(&current_subject)
                .execute(&mut *tx)
                .await?;

            // 创建新的身份记录
            let target_type = request.target_type.unwrap_or(current_target_type);
            let target_id = request.target_id.or(current_target_id);
            let subject = request.subject.or(current_subject);

            for role_id in &request.role_ids {
                let new_identity_id = Uuid::new_v4();

                let insert_query = format!(
                    r#"
                    INSERT INTO "{schema_name}".user_identities
                    (id, user_id, role_id, target_type, target_id, subject, created_at, updated_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    "#
                );

                sqlx::query(&insert_query)
                    .bind(new_identity_id)
                    .bind(user_id)
                    .bind(role_id)
                    .bind(&target_type)
                    .bind(target_id)
                    .bind(&subject)
                    .bind(now)
                    .bind(now)
                    .execute(&mut *tx)
                    .await?;
            }

            tx.commit().await?;

            // 获取角色信息
            let roles = self.get_roles_by_ids(&request.role_ids).await?;

            Ok(UserIdentityResponse {
                id: identity_id, // 保持原有ID
                user_id,
                roles,
                target_type,
                target_id,
                subject,
                created_at: now,
                updated_at: now,
            })
        } else {
            // 如果没有提供角色列表，只更新其他字段
            let mut update_fields = Vec::new();
            let mut param_index = 1;

            if request.target_type.is_some() {
                update_fields.push(format!("target_type = ${}", param_index));
                param_index += 1;
            }

            if request.target_id.is_some() {
                update_fields.push(format!("target_id = ${}", param_index));
                param_index += 1;
            }

            if request.subject.is_some() {
                update_fields.push(format!("subject = ${}", param_index));
                param_index += 1;
            }

            update_fields.push(format!("updated_at = ${}", param_index));
            param_index += 1;

            if update_fields.len() <= 1 { // 只有 updated_at 字段
                return Err(anyhow!("没有提供要更新的字段"));
            }

            let update_query = format!(
                r#"
                UPDATE "{schema_name}".user_identities
                SET {}
                WHERE id = ${}
                "#,
                update_fields.join(", "),
                param_index
            );

            let mut query_builder = sqlx::query(&update_query);

            // 绑定参数
            if let Some(target_type) = &request.target_type {
                query_builder = query_builder.bind(target_type);
            }
            if let Some(target_id) = request.target_id {
                query_builder = query_builder.bind(target_id);
            }
            if let Some(subject) = &request.subject {
                query_builder = query_builder.bind(subject);
            }

            query_builder = query_builder
                .bind(now)
                .bind(identity_id);

            query_builder.execute(&mut *tx).await?;

            tx.commit().await?;

            // 返回更新后的身份信息
            self.get_user_identity_by_id(identity_id, schema_name)
                .await?
                .ok_or_else(|| anyhow!("更新后无法找到用户身份"))
        }
    }

    /// 删除用户身份
    pub async fn delete_user_identity(
        &self,
        identity_id: Uuid,
        schema_name: &str,
    ) -> Result<()> {
        let query = format!(
            r#"DELETE FROM "{schema_name}".user_identities WHERE id = $1"#
        );

        let result = sqlx::query(&query)
            .bind(identity_id)
            .execute(&self.pool)
            .await?;

        if result.rows_affected() == 0 {
            return Err(anyhow!("用户身份不存在或已被删除"));
        }

        Ok(())
    }

    /// 批量创建用户身份
    pub async fn batch_create_user_identities(
        &self,
        request: BatchUserIdentityRequest,
        schema_name: &str,
    ) -> Result<BatchOperationResponse> {
        let mut success_count = 0;
        let mut failed_count = 0;
        let mut failed_items = Vec::new();

        for (index, identity_request) in request.identities.iter().enumerate() {
            match self.create_user_identity(identity_request.clone(), schema_name).await {
                Ok(_) => success_count += 1,
                Err(e) => {
                    failed_count += 1;
                    failed_items.push(BatchFailedItem {
                        index: index as u32,
                        error: e.to_string(),
                    });
                }
            }
        }

        Ok(BatchOperationResponse {
            success_count,
            failed_count,
            failed_items,
        })
    }
}
