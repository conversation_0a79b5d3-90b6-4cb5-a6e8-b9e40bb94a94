use serde::Serialize;
use sqlx::{pool::PoolConnection, PgPool, Postgres};
use uuid::Uuid;
use crate::model::homework_students::homework_students::{HomeworkStudentStatus, HomeworkStudents};
use crate::model::Student;

/**
 * 作者：张瀚
 * 说明：作业和学生关联表的数据库方法
 */
pub struct HomeworkStudentsRepository {}

impl HomeworkStudentsRepository {
    /**
     * 作者：张瀚
     * 说明：通过作业ID查询涉及的学生列表
     */
    pub async fn find_student_list_by_homework_id(conn: &mut PoolConnection<Postgres>, homework_id: &Uuid) -> Result<Vec<Student>, String> {
        sqlx::query_as::<_, Student>("SELECT s.* FROM students s ,homework_students hs WHERE hs.homework_id = $1 AND s.id = hs.student_id ORDER BY s.created_at DESC")
            .bind(homework_id)
            .fetch_all(conn.as_mut())
            .await
            .map_err(|e| e.to_string())
            .map(|data| data)
    }
    pub async fn fetch_class_ids_by_homework_id(db: &PgPool, schema_name: &str, homework_id: &Uuid) -> anyhow::Result<Vec<(Uuid, i64)>> {
        let mut builder = sqlx::QueryBuilder::new(format!("SELECT class_id, COUNT(*) FROM {}.homework_students WHERE homework_id = ", schema_name));
        builder.push_bind(homework_id).push(" GROUP BY class_id");
        let ret = builder.build_query_as().fetch_all(db).await?;
        Ok(ret)
    }

    pub async fn fetch_homework_students_by_homework_id(db: &PgPool, schema_name: &str, homework_id: &Uuid) -> Result<Vec<HomeworkStudents>, String> {
        let mut builder = sqlx::QueryBuilder::new(format!("SELECT * FROM {}.homework_students WHERE homework_id = ", schema_name));
        builder.push_bind(homework_id).push(" order by created_at desc");
        builder.build_query_as().fetch_all(db).await.map_err(|e| e.to_string())
    }

    /// 通过作业ID和学生ID查询该学生是否参与这块信息
    pub async fn get_student_by_homework_id_and_student_id(conn: &mut PoolConnection<Postgres>, homework_id: &Uuid, student_id: &Uuid) -> Result<Vec<Student>, String> {
        let query = " SELECT s.* FROM students s INNER JOIN homework_students hs  ON s.id = hs.homework_id WHERE 1=1 ";
        let mut builder = sqlx::QueryBuilder::new(query);

        builder.push(" hs.homework_id = ").push_bind(homework_id)
            .push(" hs.student_id = ").push_bind(student_id)
            .push(" ORDER BY s.created_at DESC ")
            .build_query_as::<Student>()
            .fetch_all(conn.as_mut())
            .await
            .map_err(|e| e.to_string())
            .map(|data| data)
    }
    pub async fn update_homework_student_status(db: &PgPool, tenant_name: &str, student_id: Uuid, status: HomeworkStudentStatus) -> anyhow::Result<()> {
        let mut builder = sqlx::QueryBuilder::new(format!("UPDATE {}.homework_students SET status = ", tenant_name));
        builder.push_bind(status).push(" WHERE student_id = ").push_bind(student_id);
        builder.build().execute(db).await?;
        Ok(())
    }
    pub async fn fetch_homework_students_by_homework_class(db: &PgPool, tenant_name: &str, homework_id: Uuid, class_id: Option<Uuid>) -> anyhow::Result<Vec<HomeworkStudents>> {
        let mut builder = sqlx::QueryBuilder::new(format!("SELECT * FROM {}.homework_students WHERE homework_id = ", tenant_name));
        builder.push_bind(homework_id);
        if let Some(class_id) = class_id {
            builder.push(" AND class_id = ").push_bind(class_id);
        }
        let ret = builder.build_query_as().fetch_all(db).await?;
        Ok(ret)
    }

    pub async fn fetch_students_by_homework_class(db: &PgPool, tenant_name: &str, homework_id: Uuid, class_id: Option<Uuid>) -> anyhow::Result<Vec<HomeworkStudentBase>> {
        let mut builder = sqlx::QueryBuilder::new(format!(r#"
            SELECT hs.student_id, s.student_number, s.student_name, hs.status  FROM {}.homework_students hs
            INNER JOIN {}.students s ON s.id = hs.student_id
            WHERE hs.homework_id = "#, tenant_name, tenant_name));
        builder.push_bind(homework_id);
        if let Some(class_id) = class_id {
            builder.push(" AND hs.class_id = ").push_bind(class_id);
        }
        let ret = builder.build_query_as().fetch_all(db).await?;
        Ok(ret)
    }
    pub async fn fetch_homework_student_summary(db: &PgPool, tenant_name: &str, homework_id: Uuid) -> anyhow::Result<Vec<HomeworkStudentSummary>> {
        let mut builder = sqlx::QueryBuilder::new(format!(r#"SELECT status, COUNT(*) as count FROM {}.homework_students WHERE homework_id = "#, tenant_name));
        builder.push_bind(homework_id).push(" GROUP BY status");
        let ret = builder.build_query_as().fetch_all(db).await?;
        Ok(ret)
    }
}
#[derive(Debug, Serialize, sqlx::FromRow)]
pub struct HomeworkStudentSummary {
    pub count: i64,
    pub status: HomeworkStudentStatus,
}

#[derive(Debug, Serialize, sqlx::FromRow)]
pub struct HomeworkStudentBase {
    pub student_id: Uuid,
    pub student_number: String,
    pub student_name: String,
    pub status: HomeworkStudentStatus,
}