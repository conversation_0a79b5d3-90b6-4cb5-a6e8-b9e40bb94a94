use chrono::{NaiveDate, Utc};
use serde_json;
use sqlx::{PgConnection, PgPool};
use std::collections::HashMap;
use std::sync::Arc;
use tracing::error;
use uuid::Uuid;
use crate::model::CreateStudentTeachingClass;
use crate::model::subject_groups::subject_groups::CreateSubjectGroupsParams;
use crate::model::teacher::import::{TeacherImportRecord, TeacherImportResult};
use crate::model::teacher::{CreateTeacherParams, Teacher};
use crate::model::teaching_classes::teaching_classes::CreateTeachingClassesParams;
use crate::service::administrative_classes::administrative_classes_service::AdministrativeClassesService;
use crate::service::grade::grade_service::GradeService;
use crate::service::student::student_service::StudentService;
use crate::service::subject::SubjectService;
use crate::service::subject_groups::subject_groups_service::SubjectGroupsService;
use crate::service::teacher::teacher_service::TeacherService;
use crate::service::teaching_classes::teaching_classes_service::TeachingClassesService;
use crate::utils::error::AppError;
use crate::utils::schema::{connect_with_schema, validate_schema_name};

/// 教师导入服务
#[derive(Clone)]
pub struct TeacherImportService {
    db_pool: PgPool,
    teacher_service: TeacherService,
    subject_service: SubjectService,
    grade_service: GradeService,
    subject_groups_service: Arc<SubjectGroupsService>,
    administrative_classes_service: Arc<AdministrativeClassesService>,
    teaching_classes_service: Arc<TeachingClassesService>,
    student_service: Arc<StudentService>,
}

impl TeacherImportService {
    pub fn new(
        db_pool: PgPool,
        teacher_service: TeacherService,
        subject_service: SubjectService,
        grade_service: GradeService,
        subject_groups_service: Arc<SubjectGroupsService>,
        administrative_classes_service: Arc<AdministrativeClassesService>,
        teaching_classes_service: Arc<TeachingClassesService>,
        student_service: Arc<StudentService>,
    ) -> Self {
        Self {
            db_pool,
            teacher_service,
            subject_service,
            grade_service,
            subject_groups_service,
            administrative_classes_service,
            teaching_classes_service,
            student_service,
        }
    }

    /// 教师批量导入功能
    pub async fn import_teachers(
        &self,
        schema_name: &str,
        records: Vec<TeacherImportRecord>,
        academic_year: Option<String>,
    ) -> Result<TeacherImportResult, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await
            .map_err(|e| AppError::InternalServerError(e.to_string()))?;

        let mut result = TeacherImportResult::new();

        // 获取现有学科组映射（复用TeacherService方法）
        let mut subject_group_map = self.teacher_service.get_subject_group_map(&safe_schema).await
            .map_err(|e| AppError::InternalServerError(e.to_string()))?;
        
        // 获取现有行政班映射（复用TeacherService方法）
        let admin_class_map = self.teacher_service.get_admin_class_map(&safe_schema).await
            .map_err(|e| AppError::InternalServerError(e.to_string()))?;

        for (index, record) in records.iter().enumerate() {
            let row_num = index + 2; // Excel行号从2开始（第1行是表头）
            
            // 跳过空记录
            if record.name.trim().is_empty() {
                continue;
            }

            match self.process_teacher_import_record(
                &safe_schema,
                record,
                &mut subject_group_map,
                &admin_class_map,
                &academic_year,
                &mut conn,
            ).await {
                Ok(_teacher) => {
                    result.add_success();
                }
                Err(error) => {
                    let record_json = serde_json::to_value(record)
                        .unwrap_or_else(|_| serde_json::Value::String("序列化失败".to_string()));
                    result.add_error(row_num, error.to_string(), record_json);
                }
            }
        }

        Ok(result)
    }

    /// 处理单个教师导入记录
    async fn process_teacher_import_record(
        &self,
        schema_name: &str,
        record: &TeacherImportRecord,
        subject_group_map: &mut HashMap<String, Uuid>,
        admin_class_map: &HashMap<String, Uuid>,
        academic_year: &Option<String>,
        conn: &mut PgConnection,
    ) -> Result<Teacher, AppError> {
        // 验证必填字段
        self.validate_required_fields(record).map_err(|e| AppError::InternalServerError(e.to_string()))?;

        // 1. 检查或创建学科组
        let subject_group_id = self.ensure_subject_group_exists(
            schema_name,
            &record.subject,
            subject_group_map,
        ).await?;

        // 2. 解析班主任信息
        let homeroom_class_id = self.parse_homeroom_info(
            &record.position.as_deref().unwrap_or(""),
            admin_class_map,
        ).map_err(|e| AppError::InternalServerError(e.to_string()))?;

        // 3. 生成工号
        let employee_id = self.generate_employee_id(schema_name, conn).await
            .map_err(|e| AppError::InternalServerError(e.to_string()))?;

        // 4. 解析年级
        // let grade_level_id = self.map_grade_name_to_code(&record.grade).await?;

        // 5. 创建教师记录
        let create_params = CreateTeacherParams {
            user_id: None,
            employee_id,
            teacher_name: record.name.clone(),
            phone: record.phone.clone(),
            email: None,
            gender: Some("未知".to_string()),
            date_of_birth: None,
            id_card_number: None,
            highest_education: None,
            graduation_school: None,
            major: None,
            hire_date: Some(NaiveDate::from(Utc::now().naive_utc())),
            employment_status: "在职".to_string(),
            title: record.position.clone(),
            teaching_subjects: None,
            homeroom_class_id,
            grade_level_id: None,
            subject_group_id: None,
            office_location: None,
            is_active: true,
            bio: None,
        };

        let teacher = self.teacher_service.create_teacher(schema_name, &create_params).await
            .map_err(|e| {
                error!("创建教师记录失败：{}", e);
                AppError::InternalServerError(e.to_string())})?;

        // 6. 创建教学班
        if let Some(ref teaching_classes) = record.teaching_classes {
            if !teaching_classes.trim().is_empty() {
                self.create_teaching_classes_for_teacher(
                    schema_name,
                    &teacher,
                    teaching_classes,
                    &subject_group_id,
                    academic_year,
                    record.subject.as_str(),
                ).await.map_err(|e| AppError::InternalServerError(e.to_string()))?;
            }
        }

        Ok(teacher)
    }

    /// 验证必填字段
    fn validate_required_fields(&self, record: &TeacherImportRecord) -> Result<(), String> {
        if record.name.trim().is_empty() {
            return Err("姓名不能为空".to_string());
        }
        if record.subject.trim().is_empty() {
            return Err("科目不能为空".to_string());
        }
        let empty_string = String::new();
        let phone = record.phone.as_ref().unwrap_or(&empty_string);
        if phone.trim().is_empty() {
            return Err("手机号不能为空".to_string());
        }
        if record.grade.trim().is_empty() {
            return Err("年级不能为空".to_string());
        }

        // 验证手机号格式
        if !self.is_valid_phone(phone) {
            return Err("手机号格式不正确".to_string());
        }

        Ok(())
    }

    /// 验证手机号格式
    fn is_valid_phone(&self, phone: &str) -> bool {
        phone.len() == 11 && phone.chars().all(|c| c.is_ascii_digit())
    }

    /// 确保学科组存在，如果不存在则创建
    async fn ensure_subject_group_exists(
        &self,
        schema_name: &str,
        subject_name: &str,
        subject_group_map: &mut HashMap<String, Uuid>,
    ) -> Result<Uuid, AppError> {
        if let Some(&existing_id) = subject_group_map.get(subject_name) {
            return Ok(existing_id);
        }

        // 获取学科代码
        let subject_code = self.map_subject_name_to_code(subject_name).await?.unwrap_or_default();
        
        // 创建新的学科组（复用SubjectGroupsService方法）
        let group_name = format!("{}学科组", subject_name);
        let create_params = CreateSubjectGroupsParams {
            group_name,
            subject_code,
            description: Some(format!("{}学科教学组", subject_name)),
            leader_user_id: None,
        };

        let new_subject_group = self.subject_groups_service
            .create_subject_groups(&schema_name.to_string(), &create_params)
            .await
            .map_err(|e| AppError::InternalServerError(format!("创建学科组失败: {}", e)))?;

        let new_id = new_subject_group.id;
        subject_group_map.insert(subject_name.to_string(), new_id);
        Ok(new_id)
    }

    /// 解析班主任信息
    fn parse_homeroom_info(
        &self,
        position: &str,
        admin_class_map: &HashMap<String, Uuid>,
    ) -> Result<Option<i64>, String> {
        if position.contains("班主任") {
            // 提取班级编码，格式如：班主任（704）
            if let Some(start) = position.find('（') {
                if let Some(end) = position.find('）') {
                    let class_code = &position[start + 3..end]; // 3是"（"的字节长度
                    return if let Some(&class_id) = admin_class_map.get(class_code) {
                        Ok(Some(class_id.as_u128() as i64))
                    } else {
                        Err(format!("未找到班级编码为 {} 的行政班", class_code))
                    }
                }
            }
        }
        Ok(None)
    }

    /// 生成教师工号
    async fn generate_employee_id(
        &self,
        schema_name: &str,
        conn: &mut PgConnection,
    ) -> Result<String, String> {
        let sql = format!(
            "SELECT COUNT(*) FROM {}.teachers",
            schema_name
        );
        
        let count: i64 = sqlx::query_scalar(&sql)
            .fetch_one(conn)
            .await
            .map_err(|e| format!("查询教师数量失败: {}", e))?;

        Ok(format!("T{:06}", count + 1))
    }

    /// 创建教学班
    async fn create_teaching_classes_for_teacher(
        &self,
        schema_name: &str,
        teacher: &Teacher,
        teaching_classes_str: &str,
        subject_group_id: &Uuid,
        academic_year: &Option<String>,
        subject_name: &str
    ) -> Result<(), String> {
        let class_codes: Vec<&str> = teaching_classes_str
            .split(',')
            .map(|s| s.trim())
            .filter(|s| !s.is_empty())
            .collect();

        for class_code in class_codes {
            let class_name = format!("{}-{}", subject_name, class_code);
            
            // 复用TeachingClassesService创建教学班
            let create_params = CreateTeachingClassesParams {
                class_name,
                code: Some(class_code.to_string()),
                academic_year: Some(academic_year.as_ref().unwrap_or(&"2024-2025".to_string()).clone()),
                subject_group_id: Some(*subject_group_id),
                teacher_id: Some(teacher.id),
            };

            let teaching_class = self.teaching_classes_service
                .create_classes(&schema_name.to_string(), &create_params)
                .await
                .map_err(|e| format!("创建教学班失败: {}", e))?;

            // 根据班级编号找到对应的行政班，并创建学生与教学班关联
            self.create_student_teaching_class_associations(
                schema_name,
                class_code,
                &teaching_class.id,
                &subject_group_id,
            ).await.map_err(|e| {
                error!("创建学生教学班关联失败: {}", e);
                format!("创建学生教学班关联失败: {}", e)
            })?;
        }

        Ok(())
    }

    /// 根据班级编号创建学生与教学班关联
    async fn create_student_teaching_class_associations(
        &self,
        schema_name: &str,
        class_code: &str,
        teaching_class_id: &Uuid,
        subject_group_id: &Uuid,
    ) -> Result<(), String> {
        // 1. 使用AdministrativeClassesService根据班级编号找到对应的行政班
        let admin_class = self.administrative_classes_service
            .find_active_class_by_code(&schema_name.to_string(), &class_code.to_string())
            .await?;
        
        if let Some(admin_class) = admin_class {
            // 2. 使用AdministrativeClassesService获取该行政班的所有学生
            let find_params = crate::model::administrative_classes::administrative_classes::FindAllStudentInClassParams {
                class_id: admin_class.id,
            };
            let students = self.administrative_classes_service
                .find_all_student_in_class(&schema_name.to_string(), &find_params)
                .await?;
            
            // 3. 使用SubjectGroupsService获取学科组对应的学科代码
            let subject_name = self.get_subject_name_from_subject_group(schema_name, subject_group_id).await?;
            
            // 4. 为每个学生创建与教学班的关联
            for student in students {
                let create_association = CreateStudentTeachingClass {
                    student_id: student.id,
                    class_id: *teaching_class_id,
                };
                
                // 使用StudentService创建关联
                if let Err(e) = self.student_service
                    .add_teaching_class(schema_name, create_association)
                    .await
                {
                    error!("为学生 {} 创建教学班关联失败: {}", student.student_name, e);
                    // 继续处理其他学生，不中断整个流程
                }
            }
        } else {
            error!("未找到班级编号为 {} 的行政班", class_code);
        }
        
        Ok(())
    }

    /// 使用SubjectGroupsService根据学科组ID获取学科名称
    async fn get_subject_name_from_subject_group(
        &self,
        schema_name: &str,
        subject_group_id: &Uuid,
    ) -> Result<String, String> {
        let subject_group = self.subject_groups_service
            .as_ref()
            .get_subject_group_by_id(&schema_name.to_string(), subject_group_id)
            .await?;
            
        // 如果找到学科组，则使用学科代码作为学科名称；否则使用默认值
        Ok(subject_group
            .map(|sg| sg.subject_code)
            .unwrap_or_else(|| "未知学科".to_string()))
    }

    /// 学科名称到代码的映射（复用SubjectService）
    async fn map_subject_name_to_code(&self, subject_name: &str) -> Result<Option<String>, AppError> {
       self.subject_service.get_subject_by_name(subject_name).await?
            .map(|subject| Some(subject.code))
            .ok_or(AppError::NotFound(format!("未找到学科: {}", subject_name)))
    }

    /// 年级名称到代码的映射（复用GradeService）
    async fn map_grade_name_to_code(&self, grade_name: &str) -> Result<Option<String>, AppError> {
        let grade_name = match grade_name {
            "初一" => "七年级",
            "初二" => "八年级",
            "初三" => "九年级",
            "高一" => "十年级",
            "高二" => "十一年级",
            "高三" => "十二年级",
            _ => grade_name,
        };
        self.grade_service.get_grade_by_name(grade_name).await?
            .map(|grade| Some(grade.code))
            .ok_or(AppError::NotFound(format!("未找到年级: {}", grade_name)))
    }

}
