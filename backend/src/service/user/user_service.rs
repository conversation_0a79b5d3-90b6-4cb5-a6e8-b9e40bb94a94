use crate::utils::password::PasswordService;
use anyhow::{bail, Context, Result};
use chrono::{DateTime, Utc};
use regex::Regex;
use serde::{Deserialize, Serialize};
use sqlx::{Pg<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Query<PERSON><PERSON>er, Row};
use std::sync::Arc;
use uuid::Uuid;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct UserInfo {
    pub user_id: Uuid,
    pub username: String,
    pub roles: Vec<String>,
    pub is_admin: bool,
    pub is_teacher: bool,
    pub is_student: bool,

    pub phone_number: String,
    pub created_at: Option<DateTime<Utc>>,
    pub phone_verified: Option<bool>,
    pub is_active: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RoleAssignmentRequest {
    pub user_id: Uuid,
    pub role_type: String,
    pub tenant_id: Option<Uuid>,
}

// 分页查询参数
#[derive(Debug, Deserialize, <PERSON>lone)]
pub struct GetUsersQuery {
    pub page: Option<u32>,      // 页码，从1开始
    pub per_page: Option<u32>,  // 每页数量，默认20
    pub search: Option<String>, // 搜索关键词
    pub role_filter: Option<String>, // 角色过滤
    pub status_filter: Option<String>, // 状态过滤
}

// 分页元数据
#[derive(Debug, Serialize)]
pub struct PaginationMeta {
    pub current_page: u32,
    pub per_page: u32,
    pub total: i64,
    pub total_pages: u32,
    pub has_next: bool,
    pub has_prev: bool,
}

// 分页响应结构
#[derive(Debug, Serialize)]
pub struct PaginatedUsersResponse {
    pub users: Vec<UserInfo>,
    pub pagination: PaginationMeta,
}

// 请求数据结构
#[derive(Debug, Deserialize)]
pub struct CreateUserRequest {
    pub username: String,
    pub phone: String,
    pub password: String,
}

// 响应数据结构
#[derive(Serialize, Debug)]
pub struct UserResponse {
    pub user_id: Uuid,
    pub username: String,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct UpdateUserRequest {
    pub user_id: Uuid,            // 要修改的用户 ID
    pub password: Option<String>, // 修改密码
    pub phone: Option<String>,    // 修改手机号
    pub is_active: Option<bool>,  // 激活状态
}

// ===== User Service =====

#[derive(Clone)]
pub struct UserService {
    pool: PgPool,
    password_service: Arc<PasswordService>,
}

impl UserService {
    pub fn new(pool: PgPool, password_service: Arc<PasswordService>) -> Self {
        Self {
            pool,
            password_service,
        }
    }

    /// 获取所有用户列表 (PRD 6.3.2 compliant)
    pub async fn get_all_users(&self) -> Result<Vec<UserInfo>> {
        // 定义查询结果结构
        #[derive(sqlx::FromRow)]
        struct UserRecord {
            id: Uuid,
            username: String,
            created_at: DateTime<Utc>,
        }

        // 查询所有用户
        let user_records = sqlx::query(
            r#"
            SELECT u.id, u.phone_number, u.created_at, u.username, u.phone_verified, u.is_active
            FROM public.users u
            WHERE u.is_active = true
            ORDER BY u.created_at DESC
            "#,
        )
        .fetch_all(&self.pool)
        .await
        .context("Failed to fetch users")?;

        // 获取所有活跃租户的schema
        let tenant_schemas = sqlx::query!(
            r#"
            SELECT schema_name
            FROM public.tenants
            WHERE status = 'active'
            "#
        )
        .fetch_all(&self.pool)
        .await
        .context("Failed to fetch tenant schemas")?;

        let mut users = Vec::new();

        for user_record in user_records {
            let user_id: Uuid = user_record.get("id");
            let username: String = user_record.get("username");
            let phone_number: String = user_record.get("phone_number");
            let created_at: Option<DateTime<Utc>> = user_record.get("created_at");
            let phone_verified: Option<bool> = user_record.get("phone_verified");
            let is_active: Option<bool> = user_record.get("is_active");

            let mut roles = Vec::new();

            // 查询用户在所有租户中的角色
            for tenant in &tenant_schemas {
                let query = format!(
                    r#"
                    SELECT r.code as role_code
                    FROM "{schema_name}".user_identities ui
                    JOIN public.roles r ON ui.role_id = r.id
                    WHERE ui.user_id = $1
                    "#,
                    schema_name = tenant.schema_name
                );

                let role_records = sqlx::query(&query)
                    .bind(user_id)
                    .fetch_all(&self.pool)
                    .await
                    .unwrap_or_else(|_| Vec::new()); // Skip if schema doesn't exist

                for record in role_records {
                    let role_code: String = record.get("role_code");
                    if !roles.contains(&role_code) {
                        roles.push(role_code);
                    }
                }
            }

            // Check for system admin (special case)
            if username == "admin" {
                roles.push("admin".to_string());
            }

            let user_info = UserInfo {
                user_id,
                username: username.clone(),
                phone_number: phone_number.clone(),
                created_at,
                phone_verified: phone_verified.clone(),
                is_active: is_active.clone(),
                is_admin: roles.contains(&"admin".to_string()),
                is_teacher: roles.contains(&"teacher".to_string()),
                is_student: roles.contains(&"student".to_string()),
                roles,
            };

            users.push(user_info);
        }

        Ok(users)
    }

    /// 获取用户列表（支持分页和筛选）
    pub async fn get_users_paginated(&self, query: GetUsersQuery) -> Result<PaginatedUsersResponse> {
        let page = query.page.unwrap_or(1).max(1);
        let per_page = query.per_page.unwrap_or(20).min(100); // 限制最大每页数量为100
        let offset = ((page - 1) * per_page) as i64;
        let limit = per_page as i64;

        // 构建基础查询条件
        let mut where_conditions = vec!["u.is_active = true".to_string()];
        let mut bind_values: Vec<String> = Vec::new();

        // 搜索条件
        if let Some(search) = &query.search {
            if !search.trim().is_empty() {
                where_conditions.push("(u.username ILIKE $1 OR u.phone_number ILIKE $1)".to_string());
                bind_values.push(format!("%{}%", search.trim()));
            }
        }

        let where_clause = if where_conditions.len() > 1 {
            format!("WHERE {}", where_conditions.join(" AND "))
        } else {
            format!("WHERE {}", where_conditions[0])
        };

        // 获取总数
        let total_query = format!(
            r#"
            SELECT COUNT(*)
            FROM public.users u
            {}
            "#,
            where_clause
        );

        let total: i64 = if bind_values.is_empty() {
            sqlx::query_scalar(&total_query)
                .fetch_one(&self.pool)
                .await
                .context("Failed to count users")?
        } else {
            sqlx::query_scalar(&total_query)
                .bind(&bind_values[0])
                .fetch_one(&self.pool)
                .await
                .context("Failed to count users")?
        };

        // 获取分页数据
        let users_query = format!(
            r#"
            SELECT u.id, u.phone_number, u.created_at, u.username, u.phone_verified, u.is_active
            FROM public.users u
            {}
            ORDER BY u.created_at DESC
            LIMIT $1 OFFSET $2
            "#,
            where_clause
        );

        let user_records = if bind_values.is_empty() {
            sqlx::query(&users_query)
                .bind(limit)
                .bind(offset)
                .fetch_all(&self.pool)
                .await
                .context("Failed to fetch paginated users")?
        } else {
            sqlx::query(&users_query)
                .bind(&bind_values[0])
                .bind(limit)
                .bind(offset)
                .fetch_all(&self.pool)
                .await
                .context("Failed to fetch paginated users")?
        };

        // 获取所有活跃租户的schema
        let tenant_schemas = sqlx::query!(
            r#"
            SELECT schema_name
            FROM public.tenants
            WHERE status = 'active'
            "#
        )
        .fetch_all(&self.pool)
        .await
        .context("Failed to fetch tenant schemas")?;

        let mut users = Vec::new();

        for user_record in user_records {
            let user_id: Uuid = user_record.get("id");
            let username: String = user_record.get("username");
            let phone_number: String = user_record.get("phone_number");
            let created_at: Option<DateTime<Utc>> = user_record.get("created_at");
            let phone_verified: Option<bool> = user_record.get("phone_verified");
            let is_active: Option<bool> = user_record.get("is_active");

            let mut roles = Vec::new();

            // 查询用户在所有租户中的角色
            for tenant in &tenant_schemas {
                let query = format!(
                    r#"
                    SELECT r.code as role_code
                    FROM "{schema_name}".user_identities ui
                    JOIN public.roles r ON ui.role_id = r.id
                    WHERE ui.user_id = $1
                    "#,
                    schema_name = tenant.schema_name
                );

                let role_records = sqlx::query(&query)
                    .bind(user_id)
                    .fetch_all(&self.pool)
                    .await
                    .unwrap_or_else(|_| Vec::new()); // Skip if schema doesn't exist

                for record in role_records {
                    let role_code: String = record.get("role_code");
                    if !roles.contains(&role_code) {
                        roles.push(role_code);
                    }
                }
            }

            // Check for system admin (special case)
            if username == "admin" {
                roles.push("admin".to_string());
            }

            let user_info = UserInfo {
                user_id,
                username: username.clone(),
                phone_number: phone_number.clone(),
                created_at,
                phone_verified: phone_verified.clone(),
                is_active: is_active.clone(),
                is_admin: roles.contains(&"admin".to_string()),
                is_teacher: roles.contains(&"teacher".to_string()),
                is_student: roles.contains(&"student".to_string()),
                roles,
            };

            // 客户端角色过滤（因为角色信息需要从多个schema查询）
            if let Some(role_filter) = &query.role_filter {
                if role_filter != "all" {
                    let matches_filter = match role_filter.as_str() {
                        "admin" => user_info.is_admin,
                        "teacher" => user_info.is_teacher,
                        "student" => user_info.is_student,
                        _ => true,
                    };
                    if !matches_filter {
                        continue;
                    }
                }
            }

            // 客户端状态过滤
            if let Some(status_filter) = &query.status_filter {
                if status_filter != "all" {
                    let matches_filter = match status_filter.as_str() {
                        "active" => user_info.is_active.unwrap_or(false),
                        "inactive" => !user_info.is_active.unwrap_or(true),
                        "verified" => user_info.phone_verified.unwrap_or(false),
                        "unverified" => !user_info.phone_verified.unwrap_or(true),
                        _ => true,
                    };
                    if !matches_filter {
                        continue;
                    }
                }
            }

            users.push(user_info);
        }

        // 计算分页信息
        let total_pages = ((total as f64) / (per_page as f64)).ceil() as u32;
        let pagination = PaginationMeta {
            current_page: page,
            per_page,
            total,
            total_pages,
            has_next: page < total_pages,
            has_prev: page > 1,
        };

        Ok(PaginatedUsersResponse { users, pagination })
    }

    /// 检查用户是否存在
    pub async fn user_exists(&self, user_id: Uuid) -> Result<bool> {
        let result = sqlx::query!(
            "SELECT id FROM public.users WHERE id = $1 AND is_active = true",
            user_id
        )
        .fetch_optional(&self.pool)
        .await
        .context("Failed to check user existence")?;

        Ok(result.is_some())
    }

    /// 获取用户角色 (PRD 6.3.2 compliant - queries across all tenant schemas)
    pub async fn get_user_roles(&self, user_id: Uuid) -> Result<Vec<String>> {
        let mut roles = Vec::new();

        // 获取所有活跃租户的schema
        let tenant_schemas = sqlx::query!(
            r#"
            SELECT schema_name
            FROM public.tenants
            WHERE status = 'active'
            "#
        )
        .fetch_all(&self.pool)
        .await
        .context("Failed to fetch tenant schemas")?;

        // 查询用户在所有租户中的角色
        for tenant in &tenant_schemas {
            let query = format!(
                r#"
                SELECT r.code as role_code
                FROM "{schema_name}".user_identities ui
                JOIN public.roles r ON ui.role_id = r.id
                WHERE ui.user_id = $1
                "#,
                schema_name = tenant.schema_name
            );

            let role_records = sqlx::query(&query)
                .bind(user_id)
                .fetch_all(&self.pool)
                .await
                .unwrap_or_else(|_| Vec::new()); // Skip if schema doesn't exist

            for record in role_records {
                let role_code: String = record.get("role_code");
                if !roles.contains(&role_code) {
                    roles.push(role_code);
                }
            }
        }

        // Check for system admin (special case)
        let user = sqlx::query!(
            "SELECT phone_number FROM public.users WHERE id = $1",
            user_id
        )
        .fetch_optional(&self.pool)
        .await
        .context("Failed to fetch user")?;

        if let Some(user_record) = user {
            if user_record.phone_number == "admin" {
                roles.push("admin".to_string());
            }
        }

        Ok(roles)
    }

    pub async fn create_user(
        &self,
        create_user_request: CreateUserRequest,
    ) -> Result<UserResponse> {
        // 1. 输入验证
        self.validate_register(&create_user_request)?;

        // 2. 唯一性检查（伪代码，实际需要数据库）
        self.check_exists(&create_user_request).await?;

        // 3. 密码加盐哈希（伪代码，实际需要实现）
        let (password_hash, salt) = self
            .password_service
            .hash_password(&create_user_request.password)?;

        // 4. 插入数据库，返回 id 和 created_at
        let (user_id, created_at, username) = self
            .insert_new_user(&create_user_request, &password_hash, &salt)
            .await?;

        // 6.返回
        Ok(UserResponse {
            user_id,
            username,
            created_at,
        })
    }

    pub fn validate_register(&self, create_user_request: &CreateUserRequest) -> Result<()> {
        let CreateUserRequest {
            username,
            password,
            phone,
            ..
        } = create_user_request;

        self.validate_username(username)?;
        self.validate_password(password)?;
        self.validate_phone(phone)?;

        Ok(())
    }

    pub fn validate_username(&self, username: &str) -> Result<()> {
        // 用户名验证：3-50字符，只允许字母数字
        let username_len = username.len();
        if username_len < 3 || username_len > 50 {
            bail!("用户名长度必须在3-50字符之间");
        }
        let re = Regex::new(r"^[a-zA-Z0-9]+$").unwrap();
        if !re.is_match(username) {
            bail!("用户名只能包含大小写字母和数字");
        }
        Ok(())
    }

    pub fn validate_password(&self, password: &str) -> Result<()> {
        // 密码验证：6-64字符，仅允许小写字母、大写字母、数字、标点符号
        let password_len = password.len();
        if password_len < 6 || password_len > 64 {
            bail!("密码长度必须在6-64字符之间");
        }

        // 检查密码是否包含仅允许的字符类型
        if !password.chars().all(|ch| {
            ch.is_ascii_lowercase()
                || ch.is_ascii_uppercase()
                || ch.is_ascii_digit()
                || ch.is_ascii_punctuation()
        }) {
            bail!("密码只能包含：小写字母、大写字母、数字、标点符号");
        }
        Ok(())
    }

    pub fn validate_phone(&self, phone: &str) -> Result<()> {
        // 手机号验证：11位纯数字
        if phone.len() != 11 {
            bail!("手机号长度需为11位");
        }
        if !phone.chars().all(|c| c.is_ascii_digit()) {
            bail!("手机号只能包含数字");
        }
        Ok(())
    }

    pub async fn check_exists(&self, create_user_request: &CreateUserRequest) -> Result<()> {
        let CreateUserRequest {
            username, phone, ..
        } = create_user_request;

        self.check_username_exists(username).await?;
        self.check_phone_exists(phone).await?;

        Ok(())
    }

    pub async fn check_username_exists(&self, username: &str) -> Result<()> {
        let exists: bool =
            sqlx::query_scalar("SELECT EXISTS (SELECT 1 FROM public.users WHERE username = $1)")
                .bind(username)
                .fetch_one(&self.pool)
                .await?;

        if exists {
            bail!("用户名已被占用");
        } else {
            Ok(())
        }
    }

    pub async fn check_phone_exists(&self, phone: &str) -> Result<()> {
        let exists: bool = sqlx::query_scalar(
            "SELECT EXISTS (SELECT 1 FROM public.users WHERE phone_number = $1)",
        )
        .bind(phone)
        .fetch_one(&self.pool)
        .await?;

        if exists {
            bail!("手机号已被占用");
        } else {
            Ok(())
        }
    }

    pub async fn check_phone_with_id_exists(&self, phone: &str, id: Uuid) -> Result<()> {
        let exists: bool = sqlx::query_scalar(
            "SELECT EXISTS (SELECT 1 FROM public.users WHERE phone_number = $1 and id != $2)",
        )
        .bind(phone)
        .bind(id)
        .fetch_one(&self.pool)
        .await?;

        if exists {
            bail!("手机号已被占用");
        } else {
            Ok(())
        }
    }

    pub async fn insert_new_user(
        &self,
        request: &CreateUserRequest,
        password_hash: &str,
        salt: &str,
    ) -> Result<(Uuid, DateTime<Utc>, String)> {
        let record: (Uuid, DateTime<Utc>, String) = sqlx::query_as(
            r#"
        INSERT INTO public.users (username, phone_number, password_hash, salt)
        VALUES ($1, $2, $3, $4)
        RETURNING id, created_at,username
        "#,
        )
        .bind(&request.username)
        .bind(&request.phone)
        .bind(password_hash)
        .bind(salt)
        .fetch_one(&self.pool)
        .await?;

        Ok(record)
    }

    pub async fn update_user(&self, req: UpdateUserRequest) -> Result<()> {
        let mut conn = self.pool.acquire().await?;
        let mut update_count: i8 = 0;

        let (user_exists, is_admin): (bool, Option<bool>) = sqlx::query_as(
            r#"
                SELECT
                    EXISTS (SELECT 1 FROM public.users WHERE id = $1) AS user_exists,
                    (SELECT username = 'admin' FROM public.users WHERE id = $1) AS is_admin
                "#,
        )
        .bind(req.user_id)
        .fetch_one(&mut *conn)
        .await?;

        if !user_exists {
            bail!("用户不存在");
        }

        if let Some(true) = is_admin {
            bail!("不允许操作 admin 用户");
        }

        let mut builder: QueryBuilder<Postgres> = QueryBuilder::new("UPDATE public.users SET ");
        // 修改手机
        if let Some(phone) = &req.phone {
            self.validate_phone(phone)?;
            self.check_phone_with_id_exists(phone, req.user_id).await?;
            if update_count > 0 {
                builder.push(", ");
            }
            builder.push("phone_number = ").push_bind(phone);
            update_count += 1;
        }

        // 修改密码
        if let Some(password) = &req.password {
            self.validate_password(password)?;
            let (password_hash, salt) = self.password_service.hash_password(password)?;
            if update_count > 0 {
                builder.push(", ");
            }
            builder.push("password_hash = ").push_bind(password_hash.clone());
            builder.push(", ");
            builder.push("salt = ").push_bind(salt.clone());
            update_count += 2;
        }

        // 修改激活状态
        if let Some(active) = req.is_active {
            if update_count > 0 {
                builder.push(", ");
            }
            builder.push("is_active = ").push_bind(active);
            update_count += 1;
        }

        if update_count == 0 {
            bail!("没有需要更新的字段")
        }

        builder.push(" WHERE id = ").push_bind(req.user_id);

        let debug_sql = builder.sql(); // 返回最终拼接好的 SQL 字符串
        dbg!(debug_sql); // 打印 SQL

        // 构建并执行语句
        builder.build().execute(&mut *conn).await?;

        Ok(())
    }
}
