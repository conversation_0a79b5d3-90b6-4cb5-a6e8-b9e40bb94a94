use crate::controller::user::identities::user_identity_controller::{
    BatchOperationResponse, BatchUserIdentityRequest, CreateUserIdentityRequest,
    QueryUserIdentitiesParams, UpdateUserIdentityRequest, UserIdentitiesListResponse,
    UserIdentityResponse,
};
use crate::repository::user::identities::user_identity_repository::UserIdentityRepository;
use anyhow::Result;
use std::sync::Arc;
use uuid::Uuid;

pub struct UserIdentityService {
    repository: Arc<UserIdentityRepository>,
}

impl UserIdentityService {
    pub fn new(repository: Arc<UserIdentityRepository>) -> Self {
        Self { repository }
    }

    /// 创建用户身份
    pub async fn create_user_identity(
        &self,
        request: CreateUserIdentityRequest,
        schema_name: &str,
    ) -> Result<UserIdentityResponse> {
        // 业务逻辑验证
        self.validate_create_request(&request)?;
        
        // 调用 repository 层
        self.repository.create_user_identity(request, schema_name).await
    }

    /// 获取用户身份列表
    pub async fn get_user_identities(
        &self,
        params: QueryUserIdentitiesParams,
        schema_name: &str,
    ) -> Result<UserIdentitiesListResponse> {
        // 参数验证
        self.validate_query_params(&params)?;
        
        // 调用 repository 层
        self.repository.get_user_identities(params, schema_name).await
    }

    /// 根据ID获取用户身份
    pub async fn get_user_identity_by_id(
        &self,
        identity_id: Uuid,
        schema_name: &str,
    ) -> Result<Option<UserIdentityResponse>> {
        self.repository.get_user_identity_by_id(identity_id, schema_name).await
    }

    /// 更新用户身份
    pub async fn update_user_identity(
        &self,
        identity_id: Uuid,
        request: UpdateUserIdentityRequest,
        schema_name: &str,
    ) -> Result<UserIdentityResponse> {
        // 业务逻辑验证
        self.validate_update_request(&request)?;
        
        // 调用 repository 层
        self.repository.update_user_identity(identity_id, request, schema_name).await
    }

    /// 删除用户身份
    pub async fn delete_user_identity(
        &self,
        identity_id: Uuid,
        schema_name: &str,
    ) -> Result<()> {
        self.repository.delete_user_identity(identity_id, schema_name).await
    }

    /// 批量创建用户身份
    pub async fn batch_create_user_identities(
        &self,
        request: BatchUserIdentityRequest,
        schema_name: &str,
    ) -> Result<BatchOperationResponse> {
        // 批量验证
        for identity_request in &request.identities {
            self.validate_create_request(identity_request)?;
        }
        
        // 调用 repository 层
        self.repository.batch_create_user_identities(request, schema_name).await
    }

    // ===== 私有验证方法 =====

    /// 验证创建请求
    fn validate_create_request(&self, request: &CreateUserIdentityRequest) -> Result<()> {
        if request.role_ids.is_empty() {
            return Err(anyhow::anyhow!("至少需要选择一个角色"));
        }

        if request.role_ids.len() > 10 {
            return Err(anyhow::anyhow!("最多只能选择10个角色"));
        }

        if request.target_type.is_empty() {
            return Err(anyhow::anyhow!("目标类型不能为空"));
        }

        // 验证目标类型是否有效
        let valid_target_types = ["school", "subject_group", "grade", "class", "student"];
        if !valid_target_types.contains(&request.target_type.as_str()) {
            return Err(anyhow::anyhow!("无效的目标类型: {}", request.target_type));
        }

        Ok(())
    }

    /// 验证更新请求
    fn validate_update_request(&self, request: &UpdateUserIdentityRequest) -> Result<()> {
        if !request.role_ids.is_empty() && request.role_ids.len() > 10 {
            return Err(anyhow::anyhow!("最多只能选择10个角色"));
        }

        if let Some(target_type) = &request.target_type {
            if target_type.is_empty() {
                return Err(anyhow::anyhow!("目标类型不能为空"));
            }

            let valid_target_types = ["school", "subject_group", "grade", "class", "student"];
            if !valid_target_types.contains(&target_type.as_str()) {
                return Err(anyhow::anyhow!("无效的目标类型: {}", target_type));
            }
        }

        Ok(())
    }

    /// 验证查询参数
    fn validate_query_params(&self, params: &QueryUserIdentitiesParams) -> Result<()> {
        if let Some(page_size) = params.page_size {
            if page_size == 0 || page_size > 100 {
                return Err(anyhow::anyhow!("页面大小必须在1-100之间"));
            }
        }

        if let Some(page) = params.page {
            if page == 0 {
                return Err(anyhow::anyhow!("页码必须大于0"));
            }
        }

        Ok(())
    }
}
