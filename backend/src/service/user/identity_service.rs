use crate::model::administrative_classes::administrative_classes::AdministrativeClasses;
use crate::model::teaching_classes::teaching_classes::TeachingClasses;
use crate::model::user::auth::*;
use crate::model::{Role, Teacher};
use crate::utils::schema::{connect_with_schema, validate_schema_name};
use anyhow::Result;
use anyhow::{anyhow, bail};
use chrono::Utc;
use minio::s3::utils::utc_now;
use sqlx::{Acquire, PgPool, Row};
use std::collections::HashMap;
use tracing::{info, warn};
use uuid::Uuid;

pub struct IdentityService {
    db: PgPool,
}

impl IdentityService {
    pub fn new(db: PgPool) -> Self {
        Self { db }
    }

    /// 获取用户在指定租户schema中的所有身份信息
    pub async fn get_user_identities_in_schema(&self, user_id: Uuid, schema_name: &str) -> AuthResult<Vec<UserIdentity>> {
        let query = format!(
            r#"
            SELECT id, user_id, role_id, target_type, target_id, subject, created_at, updated_at
            FROM "{schema_name}".user_identities
            WHERE user_id = $1
            ORDER BY created_at ASC
            "#
        );

        let identities = sqlx::query_as::<_, UserIdentity>(&query).bind(user_id).fetch_all(&self.db).await?;

        Ok(identities)
    }

    /// 在指定租户schema中创建用户身份
    pub async fn create_user_identity(&self, user_id: Uuid, role_id: Uuid, target_type: &str, target_id: Option<Uuid>, subject: Option<&str>, schema_name: &str) -> AuthResult<UserIdentity> {
        let identity_id = Uuid::new_v4();
        let now = Utc::now();

        let query = format!(
            r#"
            INSERT INTO "{schema_name}".user_identities
            (id, user_id, role_id, target_type, target_id, subject, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING id, user_id, role_id, target_type, target_id, subject, created_at, updated_at
            "#
        );

        let identity = sqlx::query_as::<_, UserIdentity>(&query)
            .bind(identity_id)
            .bind(user_id)
            .bind(role_id)
            .bind(target_type)
            .bind(target_id)
            .bind(subject)
            .bind(now)
            .bind(now)
            .fetch_one(&self.db)
            .await?;

        info!("User identity created: user_id={}, identity_id={}, schema={}", user_id, identity_id, schema_name);

        Ok(identity)
    }

    /// 获取用户在指定租户schema中的特定身份
    pub async fn get_user_identity_in_schema(&self, user_id: Uuid, identity_id: Uuid, schema_name: &str) -> AuthResult<Option<UserIdentity>> {
        let query = format!(
            r#"
            SELECT id, user_id, role_id, target_type, target_id, subject, created_at, updated_at
            FROM "{schema_name}".user_identities
            WHERE user_id = $1 AND id = $2
            "#
        );

        let identity = sqlx::query_as::<_, UserIdentity>(&query).bind(user_id).bind(identity_id).fetch_optional(&self.db).await?;

        Ok(identity)
    }

    /// 更新用户身份信息
    pub async fn update_user_identity(&self, identity_id: Uuid, target_type: Option<&str>, target_id: Option<Option<Uuid>>, subject: Option<Option<&str>>, schema_name: &str) -> AuthResult<()> {
        let now = Utc::now();

        // Build dynamic update query
        let mut set_clauses = Vec::new();
        let mut param_count = 1;

        if target_type.is_some() {
            set_clauses.push(format!("target_type = ${}", param_count));
            param_count += 1;
        }
        if target_id.is_some() {
            set_clauses.push(format!("target_id = ${}", param_count));
            param_count += 1;
        }
        if subject.is_some() {
            set_clauses.push(format!("subject = ${}", param_count));
            param_count += 1;
        }

        set_clauses.push(format!("updated_at = ${}", param_count));
        param_count += 1;

        if set_clauses.is_empty() {
            return Ok(());
        }

        let query = format!(
            r#"
            UPDATE "{schema_name}".user_identities
            SET {}
            WHERE id = ${}
            "#,
            set_clauses.join(", "),
            param_count
        );

        let mut query_builder = sqlx::query(&query);

        if let Some(target_type) = target_type {
            query_builder = query_builder.bind(target_type);
        }
        if let Some(target_id) = target_id {
            query_builder = query_builder.bind(target_id);
        }
        if let Some(subject) = subject {
            query_builder = query_builder.bind(subject);
        }

        query_builder = query_builder.bind(now).bind(identity_id);

        query_builder.execute(&self.db).await?;

        info!("User identity updated: identity_id={}, schema={}", identity_id, schema_name);
        Ok(())
    }

    /// 删除用户身份
    pub async fn delete_user_identity(&self, identity_id: Uuid, schema_name: &str) -> AuthResult<()> {
        let query = format!(r#"DELETE FROM "{schema_name}".user_identities WHERE id = $1"#);

        sqlx::query(&query).bind(identity_id).execute(&self.db).await?;

        info!("User identity deleted: identity_id={}, schema={}", identity_id, schema_name);
        Ok(())
    }

    // Legacy compatibility methods (to be deprecated)
    pub async fn get_identity_suggestions(&self, _user_id: Uuid) -> AuthResult<Vec<IdentityBindingSuggestion>> {
        // Return empty suggestions for now - this feature will be reimplemented later
        warn!("get_identity_suggestions called - returning empty results (method deprecated)");
        Ok(Vec::new())
    }

    pub async fn bind_identity(&self, user_id: Uuid, request: BindIdentityRequest) -> Result<()> {
        let roles: Vec<Role> = sqlx::query_as::<_, Role>("SELECT * FROM public.roles").fetch_all(&self.db).await?;
        let map: HashMap<String, Role> = roles.into_iter().map(|r| (r.code.clone(), r)).collect();
        let schema_name = sqlx::query_scalar!("SELECT schema_name FROM public.tenants WHERE id = $1", request.tenant_id)
            .fetch_optional(&self.db)
            .await?
            .ok_or_else(|| anyhow!("未找到对应租户 ID = {} 的 schema", request.tenant_id))?;
        let safe_schema = validate_schema_name(&*schema_name)?;
        let mut conn = connect_with_schema(&self.db, &safe_schema).await?;
        let mut tx = conn.begin().await?;
        let mut user_identity_vec = vec![];
        match request.identity_type.as_str() {
            "student" => {
                let role = map.get(&"student".to_string()).ok_or_else(|| anyhow!("角色权限查询错误"))?;
                let student_id = request.extend_info.ok_or_else(|| anyhow!("无法获取学生信息"))?;

                let row = sqlx::query("SELECT id FROM students WHERE student_number = $1").bind(student_id).fetch_one(&mut *tx).await?;
                let target_id: Uuid = row.try_get("id")?;

                let time = utc_now();
                user_identity_vec.push(UserIdentity {
                    id: Uuid::new_v4(),
                    user_id,
                    role_id: role.id,
                    target_type: "student".to_string(),
                    target_id: Some(target_id),
                    subject: None,
                    created_at: time,
                    updated_at: time,
                });

                sqlx::query(
                    r#"
                    UPDATE students
                    SET user_id = $1
                    WHERE id = $2
                    "#,
                )
                .bind(user_id)
                .bind(target_id)
                .execute(&mut *tx)
                .await?;
            }
            "teacher" => {
                // 拿到老师信息teacher_id 在administrative_classes teaching_class 中查询
                // 上述双班级中权限如果存在，创建对应的useridentity记录。
                // 更新teacher表下user_id = 传入的id。

                let teacher = sqlx::query_as::<_, Teacher>(
                    r#"
                            SELECT t.*
                            FROM teachers t
                            JOIN public.users u
                              ON t.phone = u.phone_number
                            WHERE u.id = $1
                            "#,
                )
                .bind(user_id) // ✅ 这里直接绑定 Uuid
                .fetch_optional(&mut *tx)
                .await?
                .ok_or_else(|| anyhow::anyhow!("未查询到关联老师"))?;

                if teacher.user_id.is_some() {
                    bail!("老师已经被其他用户绑定")
                }

                let teacher_id = teacher.id;

                // 由于数据库结构定义，后续实现更多教师权限职能，记得在这里更新并且添加更多查询功能。 @tuip123 2025年8月19日

                // 这里是行政班查询
                let a_class_option = sqlx::query_as::<_, AdministrativeClasses>(r#"SELECT * FROM administrative_classes WHERE teacher_id = $1"#)
                    .bind(teacher_id)
                    .fetch_optional(&mut *tx)
                    .await?;

                if let Some(a_class) = a_class_option {
                    let role = map.get(&"class_teacher".to_string()).ok_or_else(|| anyhow!("角色权限查询错误"))?;
                    let time = utc_now();
                    user_identity_vec.push(UserIdentity {
                        id: Uuid::new_v4(),
                        user_id,
                        role_id: role.id,
                        target_type: "class".to_string(),
                        target_id: Some(a_class.id),
                        subject: None,
                        created_at: time,
                        updated_at: time,
                    })
                }

                // 这里是教学班查询
                let t_class_option = sqlx::query_as::<_, TeachingClasses>(r#"SELECT * FROM teaching_classes WHERE teacher_id = $1"#)
                    .bind(teacher_id)
                    .fetch_optional(&mut *tx)
                    .await?;

                if let Some(t_class) = t_class_option {
                    let role = map.get(&"teacher".to_string()).ok_or_else(|| anyhow!("角色权限查询错误"))?;
                    let time = utc_now();
                    user_identity_vec.push(UserIdentity {
                        id: Uuid::new_v4(),
                        user_id,
                        role_id: role.id,
                        target_type: "teaching_class".to_string(),
                        target_id: Some(t_class.id),
                        subject: None,
                        created_at: time,
                        updated_at: time,
                    });
                }
                if user_identity_vec.len() > 0 {
                    sqlx::query(
                        r#"
                    UPDATE teachers
                    SET user_id = $1
                    WHERE id = $2
                    "#,
                    )
                    .bind(user_id)
                    .bind(teacher_id)
                    .execute(&mut *tx)
                    .await?;
                } else {
                    bail!("教师信息未完成创建，暂时无法绑定");
                }
            }
            "parent" => {
                // todo 增加家长表信息
                let role = map.get(&"parent".to_string()).ok_or_else(|| anyhow!("角色权限查询错误"))?;
                let student_id = request.extend_info.ok_or_else(|| anyhow!("无法获取学生信息"))?;

                let row = sqlx::query("SELECT id FROM students WHERE student_name = $1").bind(student_id).fetch_one(&mut *tx).await?;
                let target_id: Uuid = row.try_get("id")?;

                let time = utc_now();
                user_identity_vec.push(UserIdentity {
                    id: Uuid::new_v4(),
                    user_id,
                    role_id: role.id,
                    target_type: "student".to_string(),
                    target_id: Some(target_id),
                    subject: None,
                    created_at: time,
                    updated_at: time,
                });
            }
            _ => {
                bail!("不支持的身份类型")
            }
        };

        let is_empty = user_identity_vec.is_empty();

        for user_identity in user_identity_vec {
            sqlx::query(
                "INSERT INTO user_identities (id, user_id, role_id, target_type, target_id, subject, created_at, updated_at)
                      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)",
            )
            .bind(user_identity.id)
            .bind(user_identity.user_id)
            .bind(user_identity.role_id)
            .bind(&user_identity.target_type)
            .bind(user_identity.target_id) // Option<Uuid>
            .bind(user_identity.subject) // Option<String>
            .bind(user_identity.created_at)
            .bind(user_identity.updated_at)
            .execute(&mut *tx) // 或 conn
            .await?;
        }

        if !is_empty {
            sqlx::query(
                r#"
        INSERT INTO public.user_tenant_links (user_id, tenant_id, access_type)
        VALUES ($1, $2, $3)
        ON CONFLICT (user_id, tenant_id) DO NOTHING
        "#,
            )
            .bind(user_id)
            .bind(request.tenant_id)
            .bind("member")
            .execute(&mut *tx)
            .await?;
        } else {
            bail!("账户信息未完成创建，暂时无法绑定");
        }

        tx.commit().await?;
        Ok(())
    }

    pub async fn switch_identity(
        &self,
        _user_id: Uuid,
        _session_id: Uuid,
        _request: SwitchIdentityRequest,
        _ip_address: Option<std::net::IpAddr>,
        _user_agent: Option<String>,
    ) -> AuthResult<SwitchIdentityResponse> {
        // This is a legacy method - for now return an error directing to use new session management
        warn!("switch_identity called - method deprecated, use new session management");
        Err(AuthError::InsufficientPermissions)
    }

    pub async fn get_identities(&self, user_id: Uuid, _username: String) -> AuthResult<Vec<IdentityInfo>> {
        let mut identities = Vec::new();
        // 获取所有活跃租户的schema
        let tenant_schemas = sqlx::query!(
            r#"
            SELECT id as tenant_id, schema_name, name as tenant_name
            FROM public.tenants
            WHERE status = 'active'
            ORDER BY created_at
            "#
        )
        .fetch_all(&self.db)
        .await?;

        // 遍历所有租户schema，查询用户在每个租户中的身份
        for tenant_record in tenant_schemas {
            let query = format!(
                r#"
                SELECT
                    ui.id as identity_id,
                    ui.role_id,
                    ui.target_type,
                    ui.target_id,
                    ui.subject,
                    r.code as role_code,
                    r.name as role_name
                FROM "{schema_name}".user_identities ui
                JOIN public.roles r ON ui.role_id = r.id
                WHERE ui.user_id = $1
                ORDER BY ui.created_at DESC
                "#,
                schema_name = tenant_record.schema_name
            );

            let identity_records = sqlx::query(&query).bind(user_id).fetch_all(&self.db).await.unwrap_or_else(|_| Vec::new()); // Skip if schema doesn't exist or query fails

            for record in identity_records {
                use sqlx::Row;
                let identity_id: Uuid = record.get("identity_id");
                let role_code: String = record.get("role_code");
                let role_name: String = record.get("role_name");

                identities.push(IdentityInfo {
                    identity_id,
                    tenant_id: tenant_record.tenant_id, // Remove Some() wrapper
                    tenant_name: Some(tenant_record.schema_name.clone()),
                    identity_type: role_code,
                    display_name: Some(role_name), // Wrap with Some()
                    is_primary: false,             // Since we don't have is_primary in the new schema
                });
            }
        }
        Ok(identities)
    }

    pub async fn verify_identity(&self, _identity_id: Uuid, _verified_by: Uuid) -> AuthResult<()> {
        warn!("verify_identity called - method deprecated");
        // This method needs to be reimplemented with tenant schema context
        Err(AuthError::InsufficientPermissions)
    }
}
