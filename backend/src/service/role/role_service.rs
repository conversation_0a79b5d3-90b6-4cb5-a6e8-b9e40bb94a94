use crate::model::base::PageResult;
use crate::model::role::{
    Role, RoleVO, CreateRoleRequest, UpdateRoleRequest, RoleQueryParams,
    UserIdentityVO, AssignRoleRequest, RoleStatistics,
    Permission
};
use crate::model::user::auth::UserIdentity;
use crate::utils::error::AppError;
use sqlx::{PgPool, Row};
use uuid::Uuid;
use std::collections::HashMap;
use tracing::{info, error};

#[derive(Clone)]
pub struct RoleService {
    pub db: PgPool,
}

impl RoleService {
    pub fn new(db: PgPool) -> Self {
        Self { db }
    }

    /// 获取角色列表（分页）
    pub async fn get_roles(&self, params: RoleQueryParams) -> Result<PageResult<RoleVO>, AppError> {
        let page = params.page.unwrap_or(1).max(1);
        let page_size = params.page_size.unwrap_or(10).min(100);
        let offset = (page - 1) * page_size;

        // 使用 QueryBuilder 来构建动态查询
        let mut count_builder = sqlx::QueryBuilder::new("SELECT COUNT(*) FROM public.roles r");
        let mut query_builder = sqlx::QueryBuilder::new(
            r#"
            SELECT
                r.id, r.name, r.code, r.description, r.category, r.level,
                r.is_system, r.is_active, r.tenant_id, r.created_by,
                r.created_at, r.updated_at
            FROM public.roles r
            "#
        );

        // 构建 WHERE 条件
        let mut has_where = false;

        // 搜索条件
        if let Some(search) = &params.search {
            let search_pattern = format!("%{}%", search);
            if !has_where {
                count_builder.push(" WHERE ");
                query_builder.push(" WHERE ");
                has_where = true;
            } else {
                count_builder.push(" AND ");
                query_builder.push(" AND ");
            }
            count_builder.push("(r.name ILIKE ");
            count_builder.push_bind(search_pattern.clone());
            count_builder.push(" OR r.code ILIKE ");
            count_builder.push_bind(search_pattern.clone());
            count_builder.push(")");

            query_builder.push("(r.name ILIKE ");
            query_builder.push_bind(search_pattern.clone());
            query_builder.push(" OR r.code ILIKE ");
            query_builder.push_bind(search_pattern);
            query_builder.push(")");
        }

        // 分类筛选
        if let Some(category) = &params.category {
            if !has_where {
                count_builder.push(" WHERE ");
                query_builder.push(" WHERE ");
                has_where = true;
            } else {
                count_builder.push(" AND ");
                query_builder.push(" AND ");
            }
            count_builder.push("r.category = ");
            count_builder.push_bind(category);
            query_builder.push("r.category = ");
            query_builder.push_bind(category);
        }

        // 级别筛选
        if let Some(level) = &params.level {
            if !has_where {
                count_builder.push(" WHERE ");
                query_builder.push(" WHERE ");
                has_where = true;
            } else {
                count_builder.push(" AND ");
                query_builder.push(" AND ");
            }
            count_builder.push("r.level = ");
            count_builder.push_bind(level.to_i32());
            query_builder.push("r.level = ");
            query_builder.push_bind(level.to_i32());
        }

        // 状态筛选
        if let Some(is_active) = params.is_active {
            if !has_where {
                count_builder.push(" WHERE ");
                query_builder.push(" WHERE ");
                has_where = true;
            } else {
                count_builder.push(" AND ");
                query_builder.push(" AND ");
            }
            count_builder.push("r.is_active = ");
            count_builder.push_bind(is_active);
            query_builder.push("r.is_active = ");
            query_builder.push_bind(is_active);
        }

        // 租户筛选
        if let Some(tenant_id) = params.tenant_id {
            if !has_where {
                count_builder.push(" WHERE ");
                query_builder.push(" WHERE ");
            } else {
                count_builder.push(" AND ");
                query_builder.push(" AND ");
            }
            count_builder.push("r.tenant_id = ");
            count_builder.push_bind(tenant_id);
            query_builder.push("r.tenant_id = ");
            query_builder.push_bind(tenant_id);
        }

        // 添加排序和分页
        query_builder.push(" ORDER BY r.level ASC, r.created_at DESC");
        query_builder.push(" LIMIT ");
        query_builder.push_bind(page_size as i64);
        query_builder.push(" OFFSET ");
        query_builder.push_bind(offset as i64);

        // 执行查询
        let total_count: i64 = count_builder
            .build_query_scalar()
            .fetch_one(&self.db)
            .await
            .map_err(|e| {
                error!("Failed to count roles: {}", e);
                AppError::DatabaseError(e)
            })?;

        let roles = query_builder
            .build_query_as::<Role>()
            .fetch_all(&self.db)
            .await
            .map_err(|e| {
                error!("Failed to fetch roles: {}", e);
                AppError::DatabaseError(e)
            })?;

        // 为每个角色获取权限信息
        let mut role_vos = Vec::new();
        for role in roles {
            let permissions = self.get_role_permissions(role.id).await?;
            role_vos.push(RoleVO {
                id: role.id,
                name: role.name,
                code: role.code,
                description: role.description,
                category: role.category,
                level: role.level,
                is_system: role.is_system,
                is_active: role.is_active,
                tenant_id: role.tenant_id,
                permissions,
                created_by: role.created_by,
                created_at: role.created_at,
                updated_at: role.updated_at,
            });
        }

        let total_pages = (total_count as f64 / page_size as f64).ceil() as i32;

        Ok(PageResult {
            data: role_vos,
            total: total_count,
            page: page as i64,
            page_size: page_size as i64,
            total_pages: total_pages as i64,
        })
    }

    /// 获取单个角色详情
    pub async fn get_role_by_id(&self, role_id: Uuid) -> Result<RoleVO, AppError> {
        let role = sqlx::query_as::<_, Role>(
            "SELECT * FROM public.roles WHERE id = $1"
        )
        .bind(role_id)
        .fetch_one(&self.db)
        .await
        .map_err(|e| match e {
            sqlx::Error::RowNotFound => AppError::NotFound("角色不存在".to_string()),
            _ => AppError::DatabaseError(e),
        })?;

        let permissions = self.get_role_permissions(role_id).await?;

        Ok(RoleVO {
            id: role.id,
            name: role.name,
            code: role.code,
            description: role.description,
            category: role.category,
            level: role.level,
            is_system: role.is_system,
            is_active: role.is_active,
            tenant_id: role.tenant_id,
            permissions,
            created_by: role.created_by,
            created_at: role.created_at,
            updated_at: role.updated_at,
        })
    }

    /// 创建角色
    pub async fn create_role(&self, request: CreateRoleRequest, created_by: Uuid) -> Result<RoleVO, AppError> {
        // 检查角色编码是否唯一
        let existing_role = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM public.roles WHERE code = $1 AND tenant_id IS NOT DISTINCT FROM $2"
        )
        .bind(&request.code)
        .bind(request.tenant_id)
        .fetch_one(&self.db)
        .await
        .map_err(|e| AppError::DatabaseError(e))?;

        if existing_role > 0 {
            return Err(AppError::BadRequest("角色编码已存在".to_string()));
        }

        // 开始事务
        let mut tx = self.db.begin().await.map_err(|e| AppError::DatabaseError(e))?;

        // 创建角色
        let role_id = Uuid::new_v4();
        let role = sqlx::query_as::<_, Role>(
            r#"
            INSERT INTO public.roles (id, name, code, description, category, level, is_active, tenant_id, created_by)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            RETURNING *
            "#
        )
        .bind(role_id)
        .bind(&request.name)
        .bind(&request.code)
        .bind(&request.description)
        .bind(&request.category)
        .bind(request.level.to_i32())
        .bind(request.is_active)
        .bind(request.tenant_id)
        .bind(created_by)
        .fetch_one(&mut *tx)
        .await
        .map_err(|e| AppError::DatabaseError(e))?;

        // 分配权限
        for permission_id in &request.permissions {
            sqlx::query(
                "INSERT INTO public.role_permissions (role_id, permission_id) VALUES ($1, $2)"
            )
            .bind(role_id)
            .bind(permission_id)
            .execute(&mut *tx)
            .await
            .map_err(|e| AppError::DatabaseError(e))?;
        }

        // 提交事务
        tx.commit().await.map_err(|e| AppError::DatabaseError(e))?;

        info!("Created role: {} ({})", role.name, role.code);

        // 获取完整的角色信息
        self.get_role_by_id(role_id).await
    }

    /// 更新角色
    pub async fn update_role(&self, role_id: Uuid, request: UpdateRoleRequest) -> Result<RoleVO, AppError> {
        // 检查角色是否存在且非系统角色
        let role = sqlx::query_as::<_, Role>(
            "SELECT * FROM public.roles WHERE id = $1"
        )
        .bind(role_id)
        .fetch_one(&self.db)
        .await
        .map_err(|e| match e {
            sqlx::Error::RowNotFound => AppError::NotFound("角色不存在".to_string()),
            _ => AppError::DatabaseError(e),
        })?;

        if role.is_system {
            return Err(AppError::BadRequest("不能修改系统预设角色".to_string()));
        }

        // 开始事务
        let mut tx = self.db.begin().await.map_err(|e| AppError::DatabaseError(e))?;

        // 更新角色基本信息
        if request.name.is_some() || request.description.is_some() || request.is_active.is_some() {
            sqlx::query(
                r#"
                UPDATE public.roles 
                SET name = COALESCE($2, name),
                    description = COALESCE($3, description),
                    is_active = COALESCE($4, is_active),
                    updated_at = NOW()
                WHERE id = $1
                "#
            )
            .bind(role_id)
            .bind(&request.name)
            .bind(&request.description)
            .bind(request.is_active)
            .execute(&mut *tx)
            .await
            .map_err(|e| AppError::DatabaseError(e))?;
        }

        // 更新权限
        if let Some(permissions) = &request.permissions {
            // 删除现有权限
            sqlx::query("DELETE FROM public.role_permissions WHERE role_id = $1")
                .bind(role_id)
                .execute(&mut *tx)
                .await
                .map_err(|e| AppError::DatabaseError(e))?;

            // 添加新权限
            for permission_id in permissions {
                sqlx::query(
                    "INSERT INTO public.role_permissions (role_id, permission_id) VALUES ($1, $2)"
                )
                .bind(role_id)
                .bind(permission_id)
                .execute(&mut *tx)
                .await
                .map_err(|e| AppError::DatabaseError(e))?;
            }
        }

        // 提交事务
        tx.commit().await.map_err(|e| AppError::DatabaseError(e))?;

        info!("Updated role: {}", role_id);

        // 返回更新后的角色信息
        self.get_role_by_id(role_id).await
    }

    /// 删除角色
    pub async fn delete_role(&self, role_id: Uuid) -> Result<(), AppError> {
        // 检查角色是否存在且非系统角色
        let role = sqlx::query_as::<_, Role>(
            "SELECT * FROM public.roles WHERE id = $1"
        )
        .bind(role_id)
        .fetch_one(&self.db)
        .await
        .map_err(|e| match e {
            sqlx::Error::RowNotFound => AppError::NotFound("角色不存在".to_string()),
            _ => AppError::DatabaseError(e),
        })?;

        if role.is_system {
            return Err(AppError::BadRequest("不能删除系统预设角色".to_string()));
        }

        // 检查是否有用户正在使用该角色 (跨所有租户schema)
        // Note: This checks across all active tenant schemas
        let tenant_schemas: Vec<String> = sqlx::query_scalar(
            "SELECT schema_name FROM public.tenants WHERE status = 'active'"
        )
        .fetch_all(&self.db)
        .await
        .map_err(|e| AppError::DatabaseError(e))?;

        let mut total_user_count = 0i64;
        for schema_name in tenant_schemas {
            let query = format!(
                "SELECT COUNT(*) FROM \"{}\".user_identities WHERE role_id = $1",
                schema_name
            );
            let count: i64 = sqlx::query_scalar(&query)
                .bind(role_id)
                .fetch_one(&self.db)
                .await
                .unwrap_or(0); // Skip if schema doesn't exist yet
            total_user_count += count;
        }
        
        if total_user_count > 0 {
            return Err(AppError::BadRequest("无法删除正在使用的角色".to_string()));
        }

        // 删除角色（级联删除权限关联）
        sqlx::query("DELETE FROM public.roles WHERE id = $1")
            .bind(role_id)
            .execute(&self.db)
            .await
            .map_err(|e| AppError::DatabaseError(e))?;

        info!("Deleted role: {}", role_id);
        Ok(())
    }

    /// 切换角色状态
    pub async fn toggle_role_status(&self, role_id: Uuid, is_active: bool) -> Result<RoleVO, AppError> {
        // 检查角色是否存在且非系统角色
        let role = sqlx::query_as::<_, Role>(
            "SELECT * FROM public.roles WHERE id = $1"
        )
        .bind(role_id)
        .fetch_one(&self.db)
        .await
        .map_err(|e| match e {
            sqlx::Error::RowNotFound => AppError::NotFound("角色不存在".to_string()),
            _ => AppError::DatabaseError(e),
        })?;

        if role.is_system {
            return Err(AppError::BadRequest("不能修改系统预设角色的状态".to_string()));
        }

        // 更新状态
        sqlx::query(
            "UPDATE public.roles SET is_active = $2, updated_at = NOW() WHERE id = $1"
        )
        .bind(role_id)
        .bind(is_active)
        .execute(&self.db)
        .await
        .map_err(|e| AppError::DatabaseError(e))?;

        info!("Toggled role status: {} -> {}", role_id, is_active);

        // 返回更新后的角色信息
        self.get_role_by_id(role_id).await
    }

    /// 获取角色的权限列表
    pub async fn get_role_permissions(&self, role_id: Uuid) -> Result<Vec<Permission>, AppError> {
        let permissions = sqlx::query_as::<_, Permission>(
            r#"
            SELECT p.* FROM public.permissions p
            INNER JOIN public.role_permissions rp ON p.id = rp.permission_id
            WHERE rp.role_id = $1
            ORDER BY p.resource, p.action
            "#
        )
        .bind(role_id)
        .fetch_all(&self.db)
        .await
        .map_err(|e| AppError::DatabaseError(e))?;

        Ok(permissions)
    }

    /// 获取角色统计信息
    pub async fn get_role_statistics(&self) -> Result<RoleStatistics, AppError> {
        // 总角色数
        let total_roles: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM public.roles")
            .fetch_one(&self.db)
            .await
            .map_err(|e| AppError::DatabaseError(e))?;

        // 启用角色数
        let active_roles: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM public.roles WHERE is_active = true")
            .fetch_one(&self.db)
            .await
            .map_err(|e| AppError::DatabaseError(e))?;

        // 系统角色数
        let system_roles: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM public.roles WHERE is_system = true")
            .fetch_one(&self.db)
            .await
            .map_err(|e| AppError::DatabaseError(e))?;

        // 租户角色数
        let tenant_roles: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM public.roles WHERE is_system = false")
            .fetch_one(&self.db)
            .await
            .map_err(|e| AppError::DatabaseError(e))?;

        // 各角色的用户数量 (跨所有租户schema)
        let roles: Vec<(Uuid, String)> = sqlx::query_as(
            "SELECT id, name FROM public.roles ORDER BY name"
        )
        .fetch_all(&self.db)
        .await
        .map_err(|e| AppError::DatabaseError(e))?;

        let tenant_schemas: Vec<String> = sqlx::query_scalar(
            "SELECT schema_name FROM public.tenants WHERE status = 'active'"
        )
        .fetch_all(&self.db)
        .await
        .map_err(|e| AppError::DatabaseError(e))?;

        let mut user_count_by_role = HashMap::new();
        
        for (role_id, role_name) in roles {
            let mut total_count = 0i64;
            
            for schema_name in &tenant_schemas {
                let query = format!(
                    "SELECT COUNT(*) FROM \"{}\".user_identities WHERE role_id = $1",
                    schema_name
                );
                let count: i64 = sqlx::query_scalar(&query)
                    .bind(role_id)
                    .fetch_one(&self.db)
                    .await
                    .unwrap_or(0); // Skip if schema doesn't exist yet
                total_count += count;
            }
            
            user_count_by_role.insert(role_name, total_count);
        }

        Ok(RoleStatistics {
            total_roles,
            active_roles,
            system_roles,
            tenant_roles,
            user_count_by_role,
        })
    }

    /// 为用户分配角色 (PRD 6.3.2 compliant)
    pub async fn assign_role(&self, request: AssignRoleRequest, schema_name: &str) -> Result<UserIdentityVO, AppError> {
        // 检查角色是否存在且启用
        let role = sqlx::query_as::<_, Role>(
            "SELECT * FROM public.roles WHERE id = $1 AND is_active = true"
        )
        .bind(request.role_id)
        .fetch_one(&self.db)
        .await
        .map_err(|e| match e {
            sqlx::Error::RowNotFound => AppError::NotFound("角色不存在或已禁用".to_string()),
            _ => AppError::DatabaseError(e),
        })?;

        // 检查是否已存在相同的身份 (使用租户schema)
        let check_query = format!(
            r#"
            SELECT COUNT(*) FROM "{schema_name}".user_identities 
            WHERE user_id = $1 AND role_id = $2 
            AND target_type = $3 AND target_id IS NOT DISTINCT FROM $4 
            AND subject IS NOT DISTINCT FROM $5
            "#
        );
        
        let existing_identity = sqlx::query_scalar::<_, i64>(&check_query)
            .bind(request.user_id)
            .bind(request.role_id)
            .bind(&request.target_type.as_ref().unwrap_or(&"school".to_string()))
            .bind(request.target_id)
            .bind(&request.subject)
            .fetch_one(&self.db)
            .await
            .map_err(|e| AppError::DatabaseError(e))?;

        if existing_identity > 0 {
            return Err(AppError::BadRequest("用户已拥有相同的角色身份".to_string()));
        }

        // 创建用户身份 (PRD 6.3.2 compliant)
        let identity_id = Uuid::new_v4();
        let target_type = request.target_type.unwrap_or_else(|| "school".to_string());
        
        let insert_query = format!(
            r#"
            INSERT INTO "{schema_name}".user_identities 
            (id, user_id, role_id, target_type, target_id, subject, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
            RETURNING id, user_id, role_id, target_type, target_id, subject, created_at, updated_at
            "#
        );
        
        let user_identity = sqlx::query_as::<_, UserIdentity>(&insert_query)
            .bind(identity_id)
            .bind(request.user_id)
            .bind(request.role_id)
            .bind(&target_type)
            .bind(request.target_id)
            .bind(&request.subject)
            .fetch_one(&self.db)
            .await
            .map_err(|e| AppError::DatabaseError(e))?;

        info!("Assigned role {} to user {} in schema {}", role.name, request.user_id, schema_name);

        // 获取角色权限信息
        let permissions = self.get_role_permissions(request.role_id).await?;
        let role_vo = RoleVO {
            id: role.id,
            name: role.name,
            code: role.code,
            description: role.description,
            category: role.category,
            level: role.level,
            is_system: role.is_system,
            is_active: role.is_active,
            tenant_id: role.tenant_id,
            permissions,
            created_by: role.created_by,
            created_at: role.created_at,
            updated_at: role.updated_at,
        };

        Ok(UserIdentityVO {
            id: user_identity.id,
            user_id: user_identity.user_id,
            tenant_id: request.tenant_id, // From request context
            role: role_vo,
            target_type: user_identity.target_type,
            target_id: user_identity.target_id,
            subject: user_identity.subject,
            created_at: user_identity.created_at,
            updated_at: user_identity.updated_at,
        })
    }

    /// 移除用户角色 (PRD 6.3.2 compliant)
    pub async fn remove_user_role(&self, identity_id: Uuid, schema_name: &str) -> Result<(), AppError> {
        let query = format!(r#"DELETE FROM "{schema_name}".user_identities WHERE id = $1"#);
        let affected_rows = sqlx::query(&query)
            .bind(identity_id)
            .execute(&self.db)
            .await
            .map_err(|e| AppError::DatabaseError(e))?
            .rows_affected();

        if affected_rows == 0 {
            return Err(AppError::NotFound("用户身份不存在".to_string()));
        }

        info!("Removed user identity: {}", identity_id);
        Ok(())
    }

    /// 获取用户在指定租户schema中的所有身份 (PRD 6.3.2 compliant)
    pub async fn get_user_identities(&self, user_id: Uuid, tenant_id: Uuid, schema_name: &str) -> Result<Vec<UserIdentityVO>, AppError> {
        let query = format!(
            r#"
            SELECT 
                ui.id, ui.user_id, ui.role_id, ui.target_type, ui.target_id, ui.subject,
                ui.created_at, ui.updated_at,
                r.id as role_id, r.name as role_name, r.code as role_code, r.description as role_description,
                r.category as role_category, r.level as role_level, r.is_system as role_is_system,
                r.is_active as role_is_active, r.tenant_id as role_tenant_id,
                r.created_by as role_created_by, r.created_at as role_created_at, r.updated_at as role_updated_at
            FROM "{schema_name}".user_identities ui
            INNER JOIN public.roles r ON ui.role_id = r.id
            WHERE ui.user_id = $1
            ORDER BY ui.created_at DESC
            "#
        );

        let identities = sqlx::query(&query)
            .bind(user_id)
            .fetch_all(&self.db)
            .await
            .map_err(|e| AppError::DatabaseError(e))?;

        let mut result = Vec::new();
        for row in identities {
            // 获取角色权限
            let role_id: Uuid = row.get("role_id");
            let permissions = self.get_role_permissions(role_id).await?;

            let role_vo = RoleVO {
                id: role_id,
                name: row.get("role_name"),
                code: row.get("role_code"),
                description: row.get("role_description"),
                category: row.get("role_category"),
                level: crate::model::role::RoleLevel::from_i32(row.get("role_level")).unwrap(),
                is_system: row.get("role_is_system"),
                is_active: row.get("role_is_active"),
                tenant_id: row.get("role_tenant_id"),
                permissions,
                created_by: row.get("role_created_by"),
                created_at: row.get("role_created_at"),
                updated_at: row.get("role_updated_at"),
            };

            result.push(UserIdentityVO {
                id: row.get("id"),
                user_id: row.get("user_id"),
                tenant_id, // From parameter context
                role: role_vo,
                target_type: row.get("target_type"),
                target_id: row.get("target_id"),
                subject: row.get("subject"),
                created_at: row.get("created_at"),
                updated_at: row.get("updated_at"),
            });
        }

        Ok(result)
    }
}