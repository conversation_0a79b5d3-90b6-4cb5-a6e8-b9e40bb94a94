use super::base::*;
use crate::model::permission::casbin_policy::CasbinPolicyRecord;
use crate::repository::casbin_policy::casbin_policy_repository::CasbinPolicyRepository;
use crate::repository::students::student_repository::StudentsRepository;
use crate::repository::students::class_repository;
use crate::repository::teaching_classes::teaching_classes_repository::TeachingClassesRepository;
use crate::repository::subject_groups::subject_group_repository::SubjectGroupRepository;
use anyhow::Result;
use sqlx::{Execute, PgPool};
use std::collections::HashMap;
use tracing::{debug, info};
use uuid::Uuid;

/// Casbin查询助手
#[derive(Debug, Clone)]
pub struct CasbinQueryHelper {
    pool: PgPool,
    variable_cache: std::sync::Arc<std::sync::RwLock<HashMap<String, Vec<Uuid>>>>,
    casbin_policy_repo: CasbinPolicyRepository,
}

impl CasbinQueryHelper {
    pub fn new(pool: PgPool) -> Self {
        let casbin_policy_repo = CasbinPolicyRepository::new(pool.clone());
        Self {
            pool,
            variable_cache: std::sync::Arc::new(std::sync::RwLock::new(HashMap::new())),
            casbin_policy_repo,
        }
    }

    /// 获取数据库连接池的引用
    pub fn get_pool(&self) -> &PgPool {
        &self.pool
    }

    /// 获取用户身份列表
    pub async fn get_user_identities(&self, context: &FilterContext) -> Result<Vec<String>> {
        // 这里应该查询用户的身份信息
        // 暂时返回基于用户身份的简单列表
        Ok(vec![context.user_identity.clone()])
    }

    /// 获取用户的学生ID（如果用户是学生）
    pub async fn get_user_student_id(&self, context: &FilterContext) -> Result<Option<Uuid>> {
        StudentsRepository::get_user_student_id(&self.pool, &context.user_id, &context.schema_name).await
    }

    /// 获取用户的子女ID列表（如果用户是家长）
    pub async fn get_user_children_ids(&self, context: &FilterContext) -> Result<Option<Vec<Uuid>>> {
        StudentsRepository::get_user_children_ids(&self.pool, &context.user_id, &context.schema_name).await
    }

    /// 获取用户教授的教学班ID列表
    pub async fn get_user_teaching_classes(&self, context: &FilterContext) -> Result<Vec<Uuid>> {
        TeachingClassesRepository::get_user_teaching_classes(&self.pool, &context.user_id, &context.schema_name).await
    }

    /// 获取用户管理的行政班ID列表（班主任）
    pub async fn get_user_managed_classes(&self, context: &FilterContext) -> Result<Vec<Uuid>> {
        class_repository::get_user_managed_classes(&self.pool, &context.user_id, &context.schema_name).await
    }

    /// 获取用户管理的学科组ID列表
    pub async fn get_user_managed_subject_groups(&self, context: &FilterContext) -> Result<Vec<Uuid>> {
        let mut group_ids = SubjectGroupRepository::get_user_managed_subject_groups(&self.pool, &context.user_id, &context.schema_name).await?;
        let teaching_subject_group_ids = self.get_subject_groups_from_teaching_classes(context).await?;
        group_ids.extend(teaching_subject_group_ids);

        // 去重处理
        group_ids.sort();
        group_ids.dedup();

        Ok(group_ids)
    }

    /// 从用户教授的教学班中获取学科组ID列表
    pub async fn get_subject_groups_from_teaching_classes(&self, context: &FilterContext) -> Result<Vec<Uuid>> {
        TeachingClassesRepository::get_subject_groups_from_teaching_classes(&self.pool, &context.user_id, &context.schema_name).await
    }

    /// 获取用户管理的年级列表
    pub async fn get_user_managed_grades(&self, context: &FilterContext) -> Result<Vec<String>> {
        class_repository::get_user_managed_grades(&self.pool, &context.user_id, &context.schema_name).await
    }

    /// 查询Casbin策略
    pub async fn query_casbin_policies(&self, user_identities: &[String], domain: &str, resource: &str, action: &str) -> Result<Vec<CasbinPolicyRecord>> {
        self.casbin_policy_repo.query_casbin_policies(user_identities, domain, resource, action).await
    }

    /// 清除缓存
    pub fn clear_cache(&self) {
        if let Ok(mut cache) = self.variable_cache.write() {
            cache.clear();
        }
    }

    /// 清除特定用户的缓存
    pub fn clear_user_cache(&self, user_id: &Uuid, tenant_id: &str) {
        if let Ok(mut cache) = self.variable_cache.write() {
            let keys_to_remove: Vec<String> = cache.keys().filter(|key| key.contains(&user_id.to_string()) && key.contains(tenant_id)).cloned().collect();

            for key in keys_to_remove {
                cache.remove(&key);
            }
        }
    }
}
