use sqlx::PgPool;
use std::collections::HashSet;
use uuid::Uuid;

use crate::model::homework_students::homework_students::HomeworkStudentStatus;
use crate::repository::homework_students::homework_students_repository::HomeworkStudentsRepository;
use crate::repository::paper_scan_page::paper_scan_page_repository::PaperScanPageRepository;
use crate::{model::homework_students::homework_students::HomeworkStudents, utils::schema::connect_with_schema};

#[derive(Clone)]
pub struct HomeworkStudentsService {
    db_pool: PgPool,
}

impl HomeworkStudentsService {
    pub fn new(db_pool: PgPool) -> Self {
        Self { db_pool }
    }
}

impl HomeworkStudentsService {
    /**
     * 作者：张瀚
     * 说明：批量绑定学生到作业中
     */
    pub async fn batch_bind_students_to_homework(&self, schema_name: &String, homework_id: Uuid, student_id_list: Vec<Uuid>, class_id: Uuid) -> Result<Vec<HomeworkStudents>, String> {
        let mut conn = connect_with_schema(&self.db_pool, schema_name).await.map_err(|e| e.to_string())?;
        //先查询已有关联
        let refs: Vec<HomeworkStudents> = HomeworkStudentsRepository::fetch_homework_students_by_homework_id(&self.db_pool, schema_name, &homework_id).await?;
        //已经有的关联不要重复，所以要去掉
        let mut student_id_set: HashSet<Uuid> = student_id_list.clone().into_iter().collect();
        for ele in refs {
            student_id_set.remove(&ele.student_id);
        }
        //新建剩余的
        if student_id_set.len() == 0 {
            return Ok(vec![]);
        }
        let mut builder = sqlx::QueryBuilder::new("INSERT INTO homework_students (homework_id,student_id,class_id) VALUES ");
        for (i, ele) in student_id_set.iter().enumerate() {
            builder.push(" ( ").push_bind(homework_id).push(" , ").push_bind(ele).push(" , ").push_bind(class_id).push(" ) ");
            if i < student_id_set.len() - 1 {
                builder.push(" , ");
            }
        }
        builder.push("RETURNING *");
        builder.build_query_as().fetch_all(&mut *conn).await.map_err(|e| e.to_string())
    }

    /**
     * 作者：张瀚
     * 说明：查询某个作业涉及的所有学生列表
     */


    /// 说明: 查询某个作业涉及的所有学生人数
    /// 作者: 萧达光
    pub async fn count_homework_student_by_homework_id(&self, schema_name: &String, homework_id: &Uuid) -> anyhow::Result<i64> {
        let mut conn = connect_with_schema(&self.db_pool, schema_name).await?;

        let count: i64 = sqlx::query_scalar(" SELECT COUNT(1) FROM homework_students WHERE homework_id = $1 ")
            .bind(homework_id)
            .fetch_one(&mut *conn)
            .await?;

        Ok(count)
    }
    /**
     * 作者：张瀚
     * 说明：从作业中批量解绑学生
     */
    pub async fn batch_unbind_students_from_homework(&self, schema_name: &String, homework_id: Uuid, student_id_list: Vec<Uuid>) -> Result<(), String> {
        let mut conn = connect_with_schema(&self.db_pool, schema_name).await.map_err(|e| e.to_string())?;
        if student_id_list.len() == 0 {
            return Ok(());
        }
        let mut builder = sqlx::QueryBuilder::new("DELETE FROM homework_students WHERE homework_id = ");
        builder.push_bind(homework_id).push(" and student_id in (");
        for (i, id) in student_id_list.iter().enumerate() {
            builder.push_bind(id);
            if i < student_id_list.len() - 1 {
                builder.push(" , ");
            }
        }
        builder.push(" )").build().execute(&mut *conn).await.map_err(|e| e.to_string())?;
        Ok(())
    }

    pub async fn update_homework_student_status(&self, schema_name: &str, homework_id: Uuid, student_id: Uuid, total_page: i32) -> anyhow::Result<()> {
        let pages = PaperScanPageRepository::fetch_scan_pages_by_student_id(&self.db_pool, schema_name, student_id, homework_id).await?;
        let status = if pages.is_empty() {
            HomeworkStudentStatus::Unsubmitted
        } else if pages.len() != total_page as usize {
            HomeworkStudentStatus::Error
        } else {
            let pages = pages.into_iter().map(|p| p.page_num).collect::<HashSet<_>>();
            if (1..=total_page).all(|x| pages.contains(&x)) {
                HomeworkStudentStatus::Done
            } else {
                HomeworkStudentStatus::Error
            }
        };
        HomeworkStudentsRepository::update_homework_student_status(&self.db_pool, schema_name, student_id, status).await?;
        Ok(())
    }
}
