use crate::controller::tenant::user::tenant_user_controller::{AddUserToTenantRequest, SearchUsersQuery, UpdateUserPermissionRequest};
use crate::model::tenant::tenant_user::TenantUserVO;
use crate::repository::tenant::user::tenant_user_repository::TenantUserRepository;
use crate::utils::error::Result;
use sqlx::PgPool;
use std::sync::Arc;
use uuid::Uuid;

#[derive(Clone)]
pub struct TenantUserService {
    repository: Arc<TenantUserRepository>,
}

impl TenantUserService {
    pub fn new(pool: PgPool) -> Self {
        Self {
            repository: Arc::new(TenantUserRepository::new(pool)),
        }
    }

    /// 获取租户用户列表
    pub async fn get_tenant_users(
        &self,
        tenant_id: Uuid,
        page: Option<i64>,
        limit: Option<i64>,
    ) -> Result<(Vec<TenantUserVO>, i64)> {
        let page = page.unwrap_or(1);
        let limit = limit.unwrap_or(20);
        
        let (users, total) = self.repository
            .get_tenant_users(tenant_id, Some(page), Some(limit))
            .await
            .map_err(|e| anyhow::anyhow!("Database error: {}", e))?;
            
        Ok((users, total))
    }

    /// 添加用户到租户
    pub async fn add_user_to_tenant(
        &self,
        tenant_id: Uuid,
        request: AddUserToTenantRequest,
    ) -> Result<TenantUserVO> {
        // 检查用户是否已在租户中
        let exists = self.repository
            .is_user_in_tenant(tenant_id, request.user_id)
            .await
            .map_err(|e| anyhow::anyhow!("Database error: {}", e))?;

        if exists {
            return Err(anyhow::anyhow!("User is already in this tenant"));
        }

        // 添加用户到租户
        let access_type = request.access_type.unwrap_or_else(|| "member".to_string());
        let _tenant_user = self.repository
            .add_user_to_tenant(tenant_id, request.user_id, access_type, None, request.expires_at)
            .await
            .map_err(|e| anyhow::anyhow!("Database error: {}", e))?;

        // 获取完整的用户信息返回
        let user_vo = self.repository
            .get_tenant_user(tenant_id, request.user_id)
            .await
            .map_err(|e| anyhow::anyhow!("Database error: {}", e))?
            .ok_or_else(|| anyhow::anyhow!("Failed to retrieve added user"))?;

        Ok(user_vo)
    }

    /// 更新用户权限
    pub async fn update_user_permission(
        &self,
        tenant_id: Uuid,
        user_id: Uuid,
        request: UpdateUserPermissionRequest,
    ) -> Result<TenantUserVO> {
        // 检查用户是否在租户中
        let exists = self.repository
            .is_user_in_tenant(tenant_id, user_id)
            .await
            .map_err(|e| anyhow::anyhow!("Database error: {}", e))?;

        if !exists {
            return Err(anyhow::anyhow!("User not found in this tenant"));
        }

        // 验证至少有一个字段需要更新
        if request.access_type.is_none() && request.expires_at.is_none() {
            return Err(anyhow::anyhow!("At least one field must be provided for update"));
        }

        // 更新用户权限并获取更新后的完整用户信息
        let updated_user = self.repository
            .update_user_permission(tenant_id, user_id, request.access_type, request.expires_at)
            .await
            .map_err(|e| anyhow::anyhow!("Database error: {}", e))?;

        Ok(updated_user)
    }

    /// 移除用户
    pub async fn remove_user_from_tenant(
        &self,
        tenant_id: Uuid,
        user_id: Uuid,
        hard_delete: Option<bool>,
    ) -> Result<()> {
        // 检查用户是否在租户中
        let exists = self.repository
            .is_user_in_tenant(tenant_id, user_id)
            .await
            .map_err(|e| anyhow::anyhow!("Database error: {}", e))?;

        if !exists {
            return Err(anyhow::anyhow!("User not found in this tenant"));
        }

        let hard_delete = hard_delete.unwrap_or(false);
        
        self.repository
            .remove_user_from_tenant(tenant_id, user_id, hard_delete)
            .await
            .map_err(|e| anyhow::anyhow!("Database error: {}", e))?;

        Ok(())
    }

    /// 搜索用户
    pub async fn search_tenant_users(
        &self,
        tenant_id: Uuid,
        query: SearchUsersQuery,
    ) -> Result<(Vec<TenantUserVO>, i64)> {
        // 验证分页参数
        let page = query.page.unwrap_or(1);
        let limit = query.limit.unwrap_or(20);

        if page < 1 {
            return Err(anyhow::anyhow!("Page must be greater than 0"));
        }
        if limit < 1 || limit > 100 {
            return Err(anyhow::anyhow!("Limit must be between 1 and 100"));
        }

        let (users, total) = self.repository
            .search_tenant_users(tenant_id, &query)
            .await
            .map_err(|e| anyhow::anyhow!("Database error: {}", e))?;

        Ok((users, total))
    }

    /// 获取单个租户用户信息
    pub async fn get_tenant_user(
        &self,
        tenant_id: Uuid,
        user_id: Uuid,
    ) -> Result<TenantUserVO> {
        let user = self.repository
            .get_tenant_user(tenant_id, user_id)
            .await
            .map_err(|e| anyhow::anyhow!("Database error: {}", e))?
            .ok_or_else(|| anyhow::anyhow!("User not found in this tenant"))?;

        Ok(user)
    }

    /// 批量添加用户到租户
    pub async fn batch_add_users_to_tenant(
        &self,
        tenant_id: Uuid,
        user_requests: Vec<AddUserToTenantRequest>,
    ) -> Result<Vec<Result<TenantUserVO>>> {
        let mut results = Vec::new();

        for request in user_requests {
            let result = self.add_user_to_tenant(tenant_id, request).await;
            results.push(result);
        }

        Ok(results)
    }

    /// 批量更新用户状态
    pub async fn batch_update_user_status(
        &self,
        tenant_id: Uuid,
        user_ids: Vec<Uuid>,
        status: String,
    ) -> Result<Vec<Result<TenantUserVO>>> {
        let mut results = Vec::new();

        for user_id in user_ids {
            let request = UpdateUserPermissionRequest {
                access_type: Some(status.clone()),
                expires_at: None,
            };
            let result = self.update_user_permission(tenant_id, user_id, request).await;
            results.push(result);
        }

        Ok(results)
    }

    /// 获取租户用户统计信息
    pub async fn get_tenant_user_stats(
        &self,
        tenant_id: Uuid,
    ) -> Result<serde_json::Value> {
        // 这里可以实现更复杂的统计逻辑
        // 目前返回基本的用户数量统计
        let (_, total_users) = self.repository
            .get_tenant_users(tenant_id, Some(1), Some(1))
            .await
            .map_err(|e| anyhow::anyhow!("Database error: {}", e))?;

        let stats = serde_json::json!({
            "totalUsers": total_users,
            "tenantId": tenant_id
        });

        Ok(stats)
    }
}
